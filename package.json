{"name": "vitalcare", "main": "expo-router/entry", "version": "1.0.9", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "expo-clean": "expo prebuild --clean", "test": "jest --watchAll", "lint:fix": "eslint 'src/**/*.{js,jsx,ts,tsx}' --fix", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx,json,css,scss,md}'", "prepare": "husky"}, "jest": {"preset": "jest-expo"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@config-plugins/react-native-callkeep": "^9.0.0", "@expo/vector-icons": "^14.0.2", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-firebase/app": "^21.14.0", "@react-native-firebase/messaging": "^21.14.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@tamagui/babel-plugin": "^1.125.33", "@tamagui/checkbox": "^1.125.33", "@tamagui/config": "^1.125.33", "@tamagui/core": "^1.125.33", "@tamagui/lucide-icons": "^1.125.33", "@tanstack/react-query": "^5.67.1", "@zoom/react-native-videosdk": "2.2.0", "axios": "^1.8.1", "expo": "~52.0.46", "expo-application": "~6.0.2", "expo-av": "^15.1.4", "expo-blur": "~14.0.3", "expo-constants": "~17.0.6", "expo-dev-client": "~5.0.20", "expo-font": "~13.0.3", "expo-haptics": "~14.0.1", "expo-linking": "~7.0.5", "expo-router": "~4.0.17", "expo-secure-store": "^14.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.2", "lucide-react-native": "^0.479.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-callkeep": "^4.3.16", "react-native-device-info": "^14.0.4", "react-native-gesture-handler": "~2.20.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-permissions": "^5.4.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-sound": "^0.11.2", "react-native-svg": "15.8.0", "react-native-uuid": "^2.0.3", "react-native-voip-push-notification": "^3.3.3", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "socket.io-client": "^4.8.1", "sp-react-native-in-app-updates": "^1.4.1", "tamagui": "^1.125.33", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "eslint": "^8.57.0", "eslint-config-expo": "~8.0.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "expo-build-properties": "^0.14.6", "husky": "^9.1.7", "jest": "^29.2.1", "jest-expo": "~52.0.4", "lint-staged": "^15.4.3", "prettier": "^3.5.1", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}