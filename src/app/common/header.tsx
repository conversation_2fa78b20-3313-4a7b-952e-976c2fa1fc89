import { HeaderStyle } from "@/styles/HeaderStyle";
import { Pressable } from "react-native";
import { Avatar, Image, XStack, Text, YStack } from "tamagui";
import { useAuth } from "~/context/AuthContext";
import UserAlerts from "./userAlerts";
// Import styles

interface HeaderProps {
  onAvatarPress?: () => void;
}

export default function Header({ onAvatarPress }: HeaderProps) {
  const { localUser } = useAuth();
  const initials = localUser
    ? localUser?.firstName?.charAt(0) + localUser?.lastName?.charAt(0)
    : "User";
  return (
    <XStack {...HeaderStyle.container}>
      <Image
        source={require("../../assets/images/vital-care-login-logo.png")}
        {...HeaderStyle.logo}
      />

      <XStack {...HeaderStyle.container}>
        <UserAlerts />

        <Pressable onPress={onAvatarPress}>
          <Avatar {...HeaderStyle.avatarContainer}>
            <Avatar.Fallback
              style={{
                backgroundColor: "#1570EF",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text style={{ color: "white" }}>{initials}</Text>
            </Avatar.Fallback>
          </Avatar>
        </Pressable>
      </XStack>
    </XStack>
  );
}
