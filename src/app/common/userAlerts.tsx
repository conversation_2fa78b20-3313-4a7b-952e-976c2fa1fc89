import React from "react";
import { Pressable } from "react-native";
import { XStack } from "tamagui";
import FontAwesome6 from "@expo/vector-icons/FontAwesome6";
import CustomBadge from "./customBadge";
import { useRouter } from "expo-router";
import { useAlertsContext } from "src/context/AlertsContext";
import { useAuth } from "~/context/AuthContext";
import { MessageCircle } from "lucide-react-native";
import { useTheme } from "@/_layout";

const UserAlerts = () => {
  const router = useRouter();
  const { count } = useAlertsContext();
  const { user } = useAuth();
  const role = user?.role;
  const { theme } = useTheme();
  const isDarktheme = theme === "dark";
  return (
    <XStack position="relative" style={{ marginRight: 18 }}>
      <Pressable
        onPress={() => {
          if (!role) return;
          router.push(`/${role}/messages`);
        }}
      >
        <MessageCircle size={30} color={isDarktheme ? "white" : "black"} />
        {count > 0 && <CustomBadge count={count} />}
      </Pressable>
    </XStack>
  );
};

export default UserAlerts;
