export const useDashboardStyles = () => {
  return {
    container: { flex: 1, backgroundColor: "$screenBackgroundcolor" },
    mainStack: {
      marginInline: 15,
      marginBlockStart: 10,
      flex: 1,
      height: "100%" as "100%",
    },
    requestButton: {
      backgroundColor: "$primaryColor",
      color: "$buttonWhiteColor" as any,
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      marginBlockStart: 15,
    },
    requestIcon: { width: 20, height: 20 },
    consultationTitle: {
      fontSize: 20,
      fontWeight: "600" as any,
      marginBlockStart: 30,
      color: "$textcolor" as any,
    },
    searchContainer: { marginBlockStart: 15 },
    todayLabel: {
      fontSize: 14,
      fontWeight: "600" as "600",
      marginBlockStart: 25,
      color: "$textcolor" as any,
    },
    scrollContainer: { flexGrow: 1, paddingBottom: 20 },
  };
};
