export const useRequestNewVisitStyle = (contentHeight: number) => {
  return {
    screenParent: { backgroundColor: "$screenBackgroundcolor", flex: 1 },
    container: {
      flex: 1,
      backgroundColor: "$screenBackgroundcolor",
      marginBlockStart: "$1" as any,
      marginBlockEnd: 10,
      marginInline: 20,
    },
    mainStack: { flex: 1, marginBlockStart: "$1" as any },
    patientConsentButton: {
      backgroundColor: "$primaryColor" as any,
      color: "$buttonWhiteColor" as any,
      fontWeight: "600" as any,
      marginBlockStart: 10,
      size: "$4" as any,
      fontSize: 16,
    },
    profileCard: {
      paddingBlock: 0,
      backgroundColor: "$screenBackgroundcolor" as any,
    },
    profileContent: {
      justifyContent: "center",
      alignItems: "center",
      padding: 20,
      flex: 1,
    },
    profileText: {
      fontWeight: 500 as any,
      fontSize: 14,
      marginBlock: 10,
    },
    emailText: {
      fontWeight: 400 as any,
      fontSize: 14,
    },
    facilityContainer: {
      marginBlockStart: "$4" as any,
    },
    facilityTitle: {
      fontWeight: 500 as any,
      fontSize: 14,
      marginBlockEnd: 10,
    },
    title: {
      marginBlockStart: 10,
    },
    profileContainer: {
      borderRadius: "$5" as "$5",
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      marginBlock: 20,
    },
    signOut: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      size: "$3" as "$3",
      marginBlockStart: 15,
      color: "$textcolor" as any,
      fontWeight: 600 as any,
      fontSize: 14,
    },
    complaintContainer: {
      marginBlockEnd: 20,
      marginInline: 20,
    },

    complaintTextArea: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      size: "$1" as "$1",
      bordeWidth: 1,
      borderRadius: 7,
      color: "$textcolor" as any,
      fontWeight: 500 as any,
      padding: 10,
      fontSize: 16,
      numberOfLines: contentHeight > 780 ? 7 : 6,
      textAlignVertical: "top" as any,
    },
    avatar: {
      size:
        contentHeight > 780 ? "$6" : contentHeight > 700 ? "$6" : ("$5" as any),
    },
  };
};
