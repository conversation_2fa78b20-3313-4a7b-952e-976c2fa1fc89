import React, { useCallback, useEffect, useState } from "react";
import { Button, Image, Text, View, YStack } from "tamagui";
import Header from "@/common/header";
import SearchInput from "src/components/SearchInput";
import SheetDemo from "src/components/SettingsDrawer";
import Consultations from "./consultations";
import { useDashboardStyles } from "./Styles/DashboardStyle";
import { useRouter } from "expo-router";

export default function Dashboard() {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500);

    return () => clearTimeout(handler); // Cleanup previous timeout
  }, [searchQuery]);

  const router = useRouter();

  const handlePress = useCallback(() => setOpen(true), []);
  const requestNewVisit = () => router.push("/nurse/new-visit");
  return (
    <View {...useDashboardStyles().container}>
      <YStack {...useDashboardStyles().mainStack}>
        <Header onAvatarPress={handlePress} />
        <Button
          {...useDashboardStyles().requestButton}
          icon={
            <Image
              source={require("../../assets/images/request-new-visit-icon.png")}
              {...useDashboardStyles().requestIcon}
            />
          }
          onPress={requestNewVisit}
        >
          Request New Visit
        </Button>
        <Text {...useDashboardStyles().consultationTitle}>Consultations</Text>
        <YStack {...useDashboardStyles().searchContainer}>
          <SearchInput onSearchChange={setSearchQuery} />
          <Text {...useDashboardStyles().todayLabel}>TODAY</Text>
        </YStack>
        <YStack flex={1}>
          <Consultations searchQuery={debouncedSearchQuery} />
        </YStack>
        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </YStack>
    </View>
  );
}
