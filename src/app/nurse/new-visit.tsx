import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Text, TextArea, View, YStack } from "tamagui";
import { useRequestNewVisitStyle } from "./Styles/RequestNewVisitStyle";
import ScreenHeader from "src/components/ScreenHeader";
import { useRouter } from "expo-router";
import { useEffect, useState, useRef } from "react";
import SheetDemo from "src/components/SettingsDrawer";
import { ArrowRight } from "@tamagui/lucide-icons";
import FacilityDrawer from "src/components/FacilityDrawer";
import PatientSearchDrawer from "src/components/PatientSearchDrawer";
import Title from "src/components/Title";
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Keyboard,
  NativeSyntheticEvent,
  TextInputFocusEventData,
} from "react-native";
import { useFacilities } from "src/hooks/useFacilities";
import { usePatients } from "src/hooks/usePatientsByFacilities";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useAuth } from "~/context/AuthContext";

export default function RequestNewVisit() {
  const { user, signOut } = useAuth();
  const initials = user?.firstName
    ? user?.firstName.charAt(0).toUpperCase() +
      user?.lastName.charAt(0).toUpperCase()
    : "User";
  const insets = useSafeAreaInsets();
  const screenHeight = Dimensions.get("window").height;
  const contentHeight = screenHeight - (insets.top + insets.bottom);
  const transcriptStyles = useRequestNewVisitStyle(contentHeight);
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const openSettings = () => setOpen(true);
  const navigateBack = () => router.back();
  const [patientNameSearch, setPatientNameSearch] = useState<string>("Al");
  const scrollViewRef = useRef<ScrollView>(null);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const textAreaRef = useRef<any>(null);
  const [patientsCount, setPatientsCount] = useState(0);
  const [drawerPosition, setDrawerPosition] = useState<{ y: number }>({ y: 0 });
  const [needsExtraSpace, setNeedsExtraSpace] = useState(false);

  const {
    data: facilities,
    isLoading: facilitiesLoading,
    error: facilitiesError,
  } = useFacilities();

  const [openDrawer, setOpenDrawer] = useState<"facility" | "patient" | null>(
    null
  );

  const [selectedFacilityId, setSelectedFacilityId] = useState<string | null>(
    null
  );
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(
    null
  );
  const [reason, setReason] = useState<string>("");

  const {
    data: patients,
    isLoading: patientsLoading,
    error: patientsError,
  } = usePatients(selectedFacilityId || "", patientNameSearch);

  const { localUser } = useAuth();
  const handleFacilitySelect = (facilityId: string) => {
    setSelectedFacilityId(facilityId);
  };

  const handlePatientSelect = (patientId: string) => {
    setSelectedPatientId(patientId);
  };

  const telehealthConsent = () => {
    router.push({
      pathname: "/nurse/telehealthconsent",
      params: {
        patientId: selectedPatientId,
        facilityId: selectedFacilityId,
        reason,
      },
    });
  };

  useEffect(() => {
    if (patients?.length) {
      setPatientsCount(patients.length);
    }
  }, [patients]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
        if (scrollViewRef.current) {
          scrollViewRef.current.scrollToEnd({ animated: true });
        }
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const handleTextAreaFocus = (
    e: NativeSyntheticEvent<TextInputFocusEventData>
  ) => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollToEnd({ animated: true });
    }
  };

  const handleDrawerOpen = (type: "facility" | "patient") => {
    setOpenDrawer(type);
    // For facility drawer, always add space
    if (type === "facility") {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  };

  // Calculate extra space needed for patient dropdown
  const getExtraSpace = () => {
    if (openDrawer === "patient" && patientsCount >= 3) {
      // Each patient item is approximately 50px height, plus some padding
      return Math.min(patientsCount * 22, 110); // Cap at 300px to prevent too much space
    }
    return 0;
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === "ios" ? 50 : 0}
    >
      <View {...transcriptStyles.screenParent}>
        <View {...transcriptStyles.container}>
          <ScreenHeader
            onAvatarPress={openSettings}
            screenName="Back"
            onBackPress={navigateBack}
          />

          <View {...transcriptStyles.mainStack}>
            <ScrollView
              ref={scrollViewRef}
              contentContainerStyle={{
                flexGrow: 1,
                paddingBottom: 5,
              }}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
            >
              <YStack {...transcriptStyles.profileContainer}>
                <Card {...transcriptStyles.profileCard}>
                  <YStack {...transcriptStyles.profileContent}>
                    <Avatar circular {...transcriptStyles.avatar}>
                      <Avatar.Fallback
                        style={{
                          backgroundColor: "#1570EF",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <Text style={{ color: "white" }}>{initials}</Text>
                      </Avatar.Fallback>
                    </Avatar>
                    <Text {...transcriptStyles.profileText}>
                      {localUser?.firstName + " " + localUser?.lastName}
                    </Text>
                    <YStack {...transcriptStyles.title}>
                      <Title
                        text="Nurse"
                        backgroundColor={"$nurseBadgeBackgroundColor"}
                        borderColor={"$primaryBorderColor"}
                      />
                    </YStack>
                    <YStack>
                      <Button
                        {...transcriptStyles.signOut}
                        onPress={() => signOut()}
                      >
                        Sign Out
                      </Button>
                    </YStack>

                    {/* Facility Selection */}
                    <YStack {...transcriptStyles.facilityContainer}>
                      <Text {...transcriptStyles.facilityTitle}>Facility</Text>
                      <YStack>
                        {facilitiesLoading ? (
                          <Text>Loading facilities...</Text>
                        ) : facilitiesError ? (
                          <Text>Error loading facilities</Text>
                        ) : (
                          <FacilityDrawer
                            data={facilities || []}
                            placeholder="Select a Facility"
                            onSelect={(id: string) => handleFacilitySelect(id)}
                            onOpen={() => setOpenDrawer("facility")}
                            onClose={() => setOpenDrawer(null)}
                            isOpen={openDrawer === "facility"}
                          />
                        )}
                      </YStack>
                    </YStack>

                    {/* Patient Selection */}
                    <YStack {...transcriptStyles.facilityContainer}>
                      <Text {...transcriptStyles.facilityTitle}>Patient</Text>
                      <YStack>
                        <PatientSearchDrawer
                          data={patients || []}
                          placeholder="Select a Patient"
                          onSelect={(id: string) => handlePatientSelect(id)}
                          onOpen={() => handleDrawerOpen("patient")}
                          onClose={() => setOpenDrawer(null)}
                          isOpen={openDrawer === "patient"}
                          onSearch={(query: string) => {
                            setPatientNameSearch(query);
                            return Promise.resolve();
                          }}
                          disabled={!selectedFacilityId}
                          loading={patientsLoading}
                          error={patientsError?.message || ""}
                        />
                      </YStack>
                    </YStack>
                  </YStack>
                  <YStack {...transcriptStyles.complaintContainer}>
                    <Text {...transcriptStyles.facilityTitle}>
                      Chief complaint
                    </Text>
                    <YStack flex={1}>
                      <TextArea
                        ref={textAreaRef}
                        {...transcriptStyles.complaintTextArea}
                        placeholder="Please enter the details."
                        placeholderTextColor={"$textcolor"}
                        overflow="hidden"
                        onChange={(e) => setReason(e.nativeEvent.text)}
                        onFocus={handleTextAreaFocus}
                      />
                    </YStack>
                  </YStack>
                </Card>
              </YStack>
              {getExtraSpace() > 0 && (
                <View style={{ height: getExtraSpace() }} />
              )}
            </ScrollView>
          </View>

          {/* Fixed Patient Consent Button */}
          <View>
            <Button
              disabled={!selectedPatientId || !selectedFacilityId || !reason}
              iconAfter={<ArrowRight size={"$1"} />}
              {...transcriptStyles.patientConsentButton}
              onPress={telehealthConsent}
            >
              Patient Consent
            </Button>
          </View>

          {open && <SheetDemo open={open} setOpen={setOpen} />}
        </View>
      </View>
    </KeyboardAvoidingView>
  );
}
