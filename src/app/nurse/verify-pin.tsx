import { useState } from "react";
import Dialpad from "src/components/Dialpad";
import { Avatar, Button, Text, View, XStack, YStack } from "tamagui";

export default function PinVerification() {
  const [pin, setPin] = useState<string[]>([]);

  const handleKeyPress = (key: number | "backspace") => {
    setPin((prevPin) => {
      if (key === "backspace") {
        return prevPin.slice(0, -1);
      }
      if (prevPin.length < 4) {
        return [...prevPin, key.toString()];
      }
      return prevPin;
    });
  };

  return (
    <YStack {...Style.card}>
      <YStack {...Style.mainStack}>
        <YStack {...Style.profileIconContainer}>
          <Avatar {...Style.profleIcon}>
            <Avatar.Image
              accessibilityLabel="Cam"
              src={require("./../../assets/images/profile-icon.png")}
            />
            <Avatar.Fallback {...Style.fallback} />
          </Avatar>
        </YStack>
        <Text {...Style.usernameText}>Signed in as @username</Text>
        <Text {...Style.titleText}>Create PIN Code</Text>

        {/* PIN Display */}
        <XStack {...Style.pinCompartment}>
          {[...Array(4)].map((_, index) => (
            <View key={index} {...Style.pinContainer}>
              <Text {...Style.pinText}>{pin[index] || ""}</Text>
            </View>
          ))}
        </XStack>

        <Dialpad onKeyPress={handleKeyPress} />
        <Button {...Style.signOutBtn}>Sign out</Button>
      </YStack>
    </YStack>
  );
}

const Style = {
  card: {
    flex: 1,
    backgroundColor: "$screenBackgroundcolor",
  },
  mainStack: {
    marginInline: 30,
    marginBlockStart: 10,
    justifyContent: "center" as any,
    alignItems: "center" as any,
  },
  profleIcon: {
    circular: true,
    size: "$5" as "$5",
    borderColor: "$primaryBorderColor" as any,
  },
  fallback: {
    backgroundColor: "$screenBackgroundcolor" as any,
  },
  profileIconContainer: {
    marginBlock: 20,
  },
  usernameText: {
    fontSize: 14,
    fontWeight: 400 as any,
    color: "$textcolor" as any,
  },
  titleText: {
    marginBlockStart: 20,
    fontSize: 20,
    fontWeight: 600 as any,
  },
  pinCompartment: {
    marginBlock: 20,
    backgroundColor: "transparent" as any,
    flexDirection: "row" as any,
  },
  pinContainer: {
    width: 64,
    height: 64,
    marginInline: 10,
    borderRadius: 8,
    borderColor: "$primaryBorderColor" as any,
    borderWidth: 1,
    backgroundColor: "$verifyPinContainerColor",
    justifyContent: "center",
    alignItems: "center",
  },
  pinText: {
    fontSize: 24,
    fontWeight: 600 as any,
    color: "$textcolor" as any,
  },
  signOutBtn: {
    size: "$3" as any,
    borderColor: "$primaryBorderColor" as any,
    minWidth: 140 as any,
    marginBlock: 20,
    backgroundColor: "transparent" as any,
  },
};
