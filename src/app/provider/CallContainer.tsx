import React, { useRef, useState } from "react";
import { ScrollView, View, Dimensions } from "react-native";
import ProviderCallNative from "./call";
import CallDetails from "./CallDetails";

// Get device width for paging
const { width: windowWidth } = Dimensions.get("window");

const CallContainer: React.FC = () => {
  const scrollViewRef = useRef<ScrollView>(null);
  const [activeIndex, setActiveIndex] = useState(0);

  // Update active index when scrolling stops.
  const onMomentumScrollEnd = (event: any) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    setActiveIndex(Math.round(offsetX / windowWidth));
  };

  // Optional: functions to programmatically switch views.
  const goToCall = () => {
    scrollViewRef.current?.scrollTo({ x: 0, animated: true });
  };

  const goToDetails = () => {
    scrollViewRef.current?.scrollTo({ x: windowWidth, animated: true });
  };

  return (
    <ScrollView
      ref={scrollViewRef}
      horizontal
      pagingEnabled
      showsHorizontalScrollIndicator={false}
      onMomentumScrollEnd={onMomentumScrollEnd}
    >
      {/* The Call view */}
      <View style={{ width: windowWidth, flex: 1 }}>
        <ProviderCallNative goToDetails={goToDetails} />
      </View>

      {/* The Call Details view */}
      <View style={{ width: windowWidth, flex: 1 }}>
        <CallDetails goToCall={goToCall} />
      </View>
    </ScrollView>
  );
};

export default CallContainer;
