import { <PERSON><PERSON><PERSON><PERSON>, FilePlus, Search, X } from "@tamagui/lucide-icons";
import {
  <PERSON><PERSON>,
  Card,
  Text,
  YStack,
  <PERSON><PERSON>tack,
  ScrollView,
  Input,
  Spinner,
  View,
} from "tamagui";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Dimensions } from "react-native";
import { useEffect, useState } from "react";
import { useBillingCodes } from "src/hooks/useBillingCodes";
import { useBillingStyles } from "./Styles/BillingStyles";
import { HorizontalDashedLine } from "src/components/DashedLine";

export function useDebounce<T>(value: T, delay: number): T {
  const [debounced, setDebounced] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebounced(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debounced;
}

type BillingProps = {
  selectedCodes: { code: string; description: string }[];
  setSelectedCodes: React.Dispatch<
    React.SetStateAction<{ code: string; description: string }[]>
  >;
  isSubmitted: boolean;
  isAdding: "ICD" | "BILLING" | "";
  setIsAdding: React.Dispatch<React.SetStateAction<"ICD" | "BILLING" | "">>;
};

export default function ICDCodes({
  selectedCodes,
  setSelectedCodes,
  isSubmitted = false,
  setIsAdding,
  isAdding,
}: BillingProps) {
  const insets = useSafeAreaInsets();
  const screenHeight = Dimensions.get("window").height;
  const screenWidth = Dimensions.get("window").width;
  const availableHeight = screenHeight - (insets.top + insets.bottom);
  const billingStyles = useBillingStyles(availableHeight, screenWidth);
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedQuery = useDebounce(searchQuery, 500);
  const {
    data: billingCodes,
    isLoading,
    isError,
    error,
  } = useBillingCodes(debouncedQuery);
  const [availableCodes, setAvailableCodes] = useState<any[]>([]);
  useEffect(() => {
    if (billingCodes && billingCodes.length > 0) {
      const filtered = billingCodes.filter(
        (item) => !selectedCodes.some((c) => c.code === item.code)
      );

      setAvailableCodes(filtered);
    }
  }, [billingCodes, selectedCodes]);

  const filteredCodes = searchQuery
    ? availableCodes.filter(
        (item) =>
          item.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : [];

  const handleAddCode = (codeObj: { code: string; description: string }) => {
    if (!selectedCodes.some((c) => c.code === codeObj.code)) {
      setSelectedCodes([...selectedCodes, codeObj]);
      setAvailableCodes((avail) =>
        avail.filter((item) => item.code !== codeObj.code)
      );
    }
  };

  const handleRemoveCode = (codeObj: { code: string; description: string }) => {
    setSelectedCodes((sel) => sel.filter((c) => c.code !== codeObj.code));
    // put it back into the pool
    const removed = billingCodes?.find((b) => b.code === codeObj.code);
    if (removed) {
      setAvailableCodes((avail) => [...avail, removed]);
    }
  };

  if (isAdding === "BILLING") {
    return null;
  }

  return (
    <>
      <Text {...billingStyles.BillingText}>Problem List</Text>
      <Button
        {...billingStyles.assignBillingCodesBtn}
        icon={<FilePlus size={"$1"} />}
        onPress={() => {
          setIsAdding(open ? "" : "ICD");
          setOpen(!open);
        }}
        disabled={isSubmitted}
      >
        {open ? "Close" : "Add ICD Codes"}
      </Button>

      {selectedCodes.length > 0 &&
        (open ? (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{ ...billingStyles.selectedCodesScroll, gap: 5 }}
            paddingInline={10}
          >
            {/* <XStack
              {...billingStyles.selectedCodesContainer}
              style={{ flexWrap: "nowrap", overflow: "scroll" }}
            > */}
            {selectedCodes.map((code, index) => (
              <XStack
                key={index}
                {...billingStyles.selectedCodeItem}
                style={{
                  width: "auto",
                  borderWidth: 1,
                  borderColor: "#D2D6DB",
                  borderRadius: 5,
                  padding: 3,
                  marginRight: 5,
                }}
              >
                <Text
                  {...billingStyles.selectedCodeTextCode}
                  style={{
                    borderWidth: 0,
                    borderColor: "tansparent !important",
                  }}
                >
                  {code.code}
                </Text>
                <Button
                  {...billingStyles.removeCodeBtn}
                  onPress={() => handleRemoveCode(code)}
                  disabled={isSubmitted}
                  icon={<X size={"$1"} />}
                />
              </XStack>
            ))}
            {/* </XStack> */}
          </ScrollView>
        ) : (
          <ScrollView
            style={billingStyles.codesScroll}
            showsVerticalScrollIndicator={true}
          >
            {/* you can optionally wrap in a container */}
            <YStack {...billingStyles.codesContainer}>
              {selectedCodes.map((c, idx) => (
                <>
                  <XStack key={idx} {...billingStyles.selectedCodeItem}>
                    {/* Left: code box */}
                    <Text {...billingStyles.selectedCodeTextCode}>
                      {c.code}
                    </Text>

                    {/* Right: desc + X */}
                    <XStack {...billingStyles.selectedCodeRight}>
                      <Text {...billingStyles.selectedCodeDescription}>
                        {c.description}
                      </Text>
                      <Button
                        {...billingStyles.removeCodeBtn}
                        onPress={() => handleRemoveCode(c)}
                        disabled={isSubmitted}
                        icon={<X size={"$1"} />}
                      />
                    </XStack>
                  </XStack>
                  <HorizontalDashedLine
                    height={1}
                    dashLength={2}
                    dashGap={2}
                    color="#D2D6DB"
                  />
                </>
              ))}
            </YStack>
          </ScrollView>
        ))}

      {open && (
        <YStack {...billingStyles.frequentlyUsedContainer}>
          <XStack width="100%" justify="space-between">
            <XStack flex={1} {...billingStyles.searchContainer}>
              <Search size="$1" color="$textcolor" />
              <Input
                {...billingStyles.searchInput}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </XStack>

            <Button
              {...billingStyles.closeBtn}
              onPress={() => {
                setIsAdding("");
                setOpen(false);
              }}
            >
              Close
            </Button>
          </XStack>

          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{ maxHeight: availableHeight * 0.4 }}
          >
            {isLoading ? (
              <YStack
                {...billingStyles.loadingContainer}
                height={availableHeight * 0.4}
              >
                <Spinner size="large" color="$gray10" />
              </YStack>
            ) : filteredCodes.length === 0 ? (
              <Text {...billingStyles.noResultsText}></Text>
            ) : (
              filteredCodes.map((item, index) => (
                <>
                  <XStack
                    key={index}
                    {...billingStyles.frequentlyUsedItem}
                    onPress={() => handleAddCode(item)}
                  >
                    <YStack {...billingStyles.codeContainer}>
                      <Text {...billingStyles.frequentlyUsedCode}>
                        {item.code}
                      </Text>
                    </YStack>
                    <Text {...billingStyles.frequentlyUsedDescription}>
                      {item.description}
                    </Text>
                  </XStack>
                  <HorizontalDashedLine
                    height={1}
                    dashLength={2}
                    dashGap={2}
                    color="#D2D6DB"
                  />
                </>
              ))
            )}
          </ScrollView>
        </YStack>
      )}

      {!open && selectedCodes.length === 0 && (
        <YStack {...billingStyles.noBiilingCodestabBody}>
          <Card {...billingStyles.noBillingCodetabCard}>
            <YStack {...billingStyles.noBillingodesCard} paddingBlock={20}>
              <CircleAlert size={"$2"} />
              <Text {...billingStyles.noBillingCodesAddedText}>
                No ICD codes added
              </Text>
            </YStack>
          </Card>
        </YStack>
      )}
    </>
  );
}
