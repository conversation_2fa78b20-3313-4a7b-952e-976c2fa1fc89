import { Stack } from "expo-router";
import { View } from "tamagui";
import { AlertsProvider } from "~/context/AlertsContext";
import { useDashboardStyles } from "./Styles/DashboardStyle";
import ProviderSocketListener from "src/components/ProviderSocketListener";
import { SocketProvider } from "~/context/ProviderSocketContext"; // Import SocketProvider
import io, { Socket } from "socket.io-client";
import Constants from "expo-constants";
import { useAuth } from "~/context/AuthContext"; // Import useAuth
import React, { useEffect, useState } from "react";
import { usePushNotifications } from "src/hooks/usePushNotifications";
import { useVoipNotifications } from "src/hooks/useVoipNotifications";

const SOCKET_SERVER_URL = Constants.expoConfig?.extra?.apiUrl;

export default function ProviderLayout() {
  const dashboardStyles = useDashboardStyles();
  const { container } = dashboardStyles;
  const { user } = useAuth();
  const [socket, setSocket] = useState<Socket | null>(null);
  usePushNotifications();
  useVoipNotifications();
  useEffect(() => {
    if (!user) return;

    const newSocket = io(SOCKET_SERVER_URL, {
      transports: ["websocket"],
    });

    newSocket.on("connect", () => {
      newSocket.emit("joinRoom", `user:${user.id}`);
    });

    setSocket(newSocket);

    return () => {
      newSocket.disconnect();
      setSocket(null);
    };
  }, [user]);

  return (
    <AlertsProvider>
      <SocketProvider socket={socket}>
        <View {...container}>
          <ProviderSocketListener />
          <Stack>
            <Stack.Screen name="dashboard" options={{ headerShown: false }} />
            <Stack.Screen name="chat" options={{ headerShown: false }} />
            <Stack.Screen
              name="CallContainer"
              options={{ headerShown: false, gestureEnabled: false }}
            />
            <Stack.Screen name="billing" options={{ headerShown: false }} />
            <Stack.Screen name="scheduling" options={{ headerShown: false }} />
            <Stack.Screen name="settings" options={{ headerShown: false }} />
            <Stack.Screen name="notes" options={{ headerShown: false }} />
            <Stack.Screen name="profile" options={{ headerShown: false }} />
            <Stack.Screen
              name="CallDetails"
              options={{ headerShown: false, gestureEnabled: false }}
            />
            <Stack.Screen name="reviewcall" options={{ headerShown: false }} />
            <Stack.Screen name="messages" options={{ headerShown: false }} />
          </Stack>
        </View>
      </SocketProvider>
    </AlertsProvider>
  );
}
