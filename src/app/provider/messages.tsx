import React, { useState } from "react";
import { FlatList, Pressable } from "react-native";
import { YStack, Text, View, XStack, Avatar } from "tamagui";
import { useRouter } from "expo-router";
import { useAlertsContext, Alert } from "src/context/AlertsContext";
import { useAuth } from "~/context/AuthContext";
import { useMessagesStyle } from "./Styles/MessageStyle";
import ScreenHeader from "src/components/ScreenHeader";
import SheetDemo from "src/components/SettingsDrawer";
import { HorizontalDashedLine } from "src/components/DashedLine";

const MessageList = () => {
  const router = useRouter();
  const { alerts, loading, error, refreshAlerts } = useAlertsContext();
  const [open, setOpen] = useState(false);
  const openSettings = () => setOpen(true);
  const navigateBack = () => router.back();
  const { user } = useAuth();
  const style = useMessagesStyle();
  if (error) {
    return (
      <View {...style.mainContainer}>
        <YStack {...style.container}>
          <ScreenHeader
            onAvatarPress={openSettings}
            screenName="Messages"
            onBackPress={navigateBack}
          />
          <YStack
            style={{
              alignItems: "center",
              justifyContent: "center",
              padding: 20,
            }}
          >
            <Text style={{ fontSize: 16, color: "red" }}>
              Error loading messages.
            </Text>
          </YStack>
          {open && <SheetDemo open={open} setOpen={setOpen} />}
        </YStack>
      </View>
    );
  }

  function getTimeDifference(sentAt: string): string {
    const sentTime: Date = new Date(sentAt);
    const now: Date = new Date();
    const diffMs: number = Math.abs(now.getTime() - sentTime.getTime());

    const minute = 60 * 1000;
    const hour = 60 * minute;
    const day = 24 * hour;
    const week = 7 * day;

    // Less than 1 minute: "just now"
    if (diffMs < minute) {
      return "just now";
    }
    // Less than 1 hour: Show minutes only (e.g., "26min")
    else if (diffMs < hour) {
      const mins = Math.floor(diffMs / minute);
      return `${mins}min`;
    }
    // Between 1 hour and 24 hours: Show hours and minutes (e.g., "2hr 30min" or "5hr")
    else if (diffMs < day) {
      const hrs = Math.floor(diffMs / hour);
      const mins = Math.floor((diffMs % hour) / minute);
      return mins > 0 ? `${hrs}hr ${mins}min` : `${hrs}hr`;
    }
    // Between 24 hours and 7 days: Show days (e.g., "1 day", "2 days")
    else if (diffMs < week) {
      const days = Math.floor(diffMs / day);
      return `${days} day${days !== 1 ? "s" : ""}`;
    } else {
      return sentTime.toLocaleDateString("en-US", {
        month: "short",
        day: "2-digit",
      });
    }
  }

  const renderItem = ({ item, index }: { item: Alert; index: number }) => {
    const { lastMessage } = item;
    const dateTime = getTimeDifference(lastMessage.sent_at);
    const initials = lastMessage?.provider_name
      ? lastMessage.nurse_name
          .split(" ")
          .map((word) => word.charAt(0))
          .join("")
      : "N/A";
    const unRead = lastMessage?.user?.id !== user?.id && !lastMessage?.read;
    const isLastItem = index === (alerts?.length || 0) - 1;
    return (
      <Pressable
        onPress={() => {
          router.push({
            pathname: "/provider/reviewcall",
            params: {
              consultationId: lastMessage.consultation_id,
              openChat: "true",
            },
          });
        }}
      >
        <XStack
          {...style.messageContainer}
          paddingInlineStart={10}
          paddingBlock={5}
        >
          <View position="relative">
            <Avatar {...style.avatarContainer}>
              <Avatar.Fallback
                style={{
                  backgroundColor: "#B2DDFF",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Text {...style.initials}>{initials}</Text>
              </Avatar.Fallback>
            </Avatar>
            {unRead && (
              <View
                style={{
                  position: "absolute",
                  top: 2,
                  right: 2,
                  width: 12,
                  height: 12,
                  borderRadius: 6,
                  backgroundColor: "red",
                }}
              />
            )}
          </View>
          <YStack
            style={{
              padding: 10,
              flex: 1,
            }}
          >
            <XStack {...style.header} maxW={"95%"}>
              <Text
                {...style.messageTitle}
                numberOfLines={1}
                maxW={"75%"}
                fontWeight={unRead ? "800" : "500"}
              >
                {lastMessage?.nurse_name + " - " + lastMessage?.patient_name}
              </Text>
              <Text {...style.dateText} fontWeight={unRead ? "800" : "500"}>
                {dateTime}
              </Text>
            </XStack>
            <XStack maxW={"100%"}>
              <Text
                {...style.messageText}
                numberOfLines={2}
                ellipsizeMode="tail"
                fontWeight={unRead ? "600" : "400"}
              >
                {lastMessage.message}
              </Text>
            </XStack>
          </YStack>
        </XStack>
        {!isLastItem && <HorizontalDashedLine />}
      </Pressable>
    );
  };

  return (
    <View {...style.mainContainer}>
      <YStack {...style.container}>
        <ScreenHeader
          onAvatarPress={openSettings}
          screenName="Messages"
          onBackPress={navigateBack}
        />

        <YStack flex={1} marginBlockStart={20}>
          <FlatList
            data={alerts || []}
            keyExtractor={(item) => item.consultationId}
            renderItem={renderItem}
            showsVerticalScrollIndicator={false}
            onRefresh={refreshAlerts}
            refreshing={loading}
            ListEmptyComponent={
              <YStack
                style={{
                  alignItems: "center",
                  justifyContent: "center",
                  padding: 20,
                }}
              >
                <Text style={{ fontSize: 16, color: "#000" }}>
                  No unread messages.
                </Text>
              </YStack>
            }
            style={{ flex: 1 }}
          />
        </YStack>

        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </YStack>
    </View>
  );
};

export default MessageList;
