import { Consultation } from "src/components/Consultation";
import DemographicsDropDown from "src/components/DemographicsDropDown";
import DropdownTextEditor from "src/components/DropdownTextEditor";
import { But<PERSON>, ScrollView, Text, View, YStack, useTheme } from "tamagui";
import ProblemListDropDown from "./ProblemListDropdown";
import { HorizontalDashedLine } from "src/components/DashedLine";
import {
  ArrowUpDown,
  // Barbell,
  Weight,
  Ruler,
  MoveVertical,
  Wind,
  Heart,
  Thermometer,
  Frown,
  Activity,
} from "lucide-react-native";
import PhysicalExam from "src/components/PhysicalExam";
import DropdownDetails from "src/components/DropdownDetails";
import { KeyboardAvoidingView, Platform } from "react-native";

interface ProfileProps {
  onOrdersChange: (text: string) => void;
  onChangePatientDetailsSnapshot: (updateFn: (prev: any) => any) => void;
  isSubmitted: boolean;
  consultation: any;
  physicalExam: any;
  setPhysicalExam: (value: any) => void;
}

export default function Profile({
  onOrdersChange,
  onChangePatientDetailsSnapshot,
  consultation,
  isSubmitted = false,
  physicalExam,
  setPhysicalExam,
}: ProfileProps) {
  const theme = useTheme();
  const profileStyle = useProfileStyles();

  // Function to update a single field in the patient details snapshot.
  const updatePatientDetailsSnapshot = (field: string, value: string) => {
    onChangePatientDetailsSnapshot((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  const vitals = consultation?.patient_details_snapshot?.vitals || {};

  const renderVitalBoxes = () => {
    const order = [
      "heartrate",
      "bloodpressure",
      "temperature",
      "respirations",
      "painlevel",
    ];

    const sortedEntries = Object.entries(vitals).sort(([a], [b]) => {
      const aKey = a.toLowerCase();
      const bKey = b.toLowerCase();
      const ia = order.indexOf(aKey);
      const ib = order.indexOf(bKey);
      const ra = ia >= 0 ? ia : order.length;
      const rb = ib >= 0 ? ib : order.length;
      return ra - rb;
    });

    return sortedEntries.map(([key, m]: any) => {
      const label =
        key.toLowerCase() === "bloodpressure"
          ? "Blood Pressure"
          : key.charAt(0).toUpperCase() + key.slice(1);

      const recorded = m.recorded;
      let Icon: React.ComponentType<any> = Activity;
      switch (key) {
        case "weight":
          Icon = Weight;
          break;
        case "height":
          Icon = Ruler;
          break;
        case "heartrate":
          Icon = Heart;
          break;
        case "temperature":
          Icon = Thermometer;
          break;
        case "respirations":
          Icon = Wind;
          break;
        case "painLevel":
        case "painlevel":
          Icon = Frown;
          break;
        case "bloodpressure":
          Icon = ArrowUpDown;
          break;
      }

      const valueText =
        key.toLowerCase() === "bloodpressure"
          ? `${m.systolic}/${m.diastolic}${m.unit ? " " + m.unit : ""}`
          : `${m.value}${m.unit ? " " + m.unit : ""}`;

      return (
        <YStack key={key}>
          <YStack key={key} {...profileStyle.vitalBoxContainer}>
            <Text verticalAlign={"center"} fontSize={10} mb={6}>
              {label}
            </Text>
            <Icon size={24} color="black" />
            <Text
              fontSize={12}
              fontWeight="500"
              verticalAlign={"center"}
              color="$confirmOrderTextColor"
              style={{
                width: "100%",
                textAlign: "center",
              }}
            >
              {valueText}
            </Text>
          </YStack>
          {(() => {
            const [datePart, ...rest] = recorded.split(" ");
            const timePart = rest.join(" ");
            return (
              <Text {...profileStyle.vitalBoxDateAndTimeContainer}>
                {datePart}
                {"\n"}
                {timePart}
              </Text>
            );
          })()}
        </YStack>
      );
    });
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <YStack {...profileStyle.mainContainer}>
        <YStack {...profileStyle.childContainer}>
          <Text {...profileStyle.profileText}>Profile</Text>
          <ScrollView showsVerticalScrollIndicator={false}>
            <YStack marginBlockEnd={20}>
              <YStack>
                <Consultation
                  data={consultation}
                  isFromProvider
                  isFromCallScreen
                  shouldSowOrderandBadge={false}
                />
              </YStack>
              <YStack>
                <DropdownTextEditor
                  title="Orders"
                  data={consultation?.order || ""}
                  onChangeText={onOrdersChange}
                  isComingFromReviewCall
                  isSubmitted={isSubmitted}
                  orderConfirmed={consultation?.order_confirmed}
                />
                <HorizontalDashedLine
                  height={1}
                  dashLength={2}
                  dashGap={2}
                  color="#D2D6DB"
                  style={{ marginTop: 18 }}
                />
              </YStack>
              <DemographicsDropDown
                title="Demographics"
                data={consultation?.patient_details_snapshot}
              />
              {Object.keys(vitals).length > 0 && (
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <YStack flexDirection="row" gap="$3" mt="$4">
                    {renderVitalBoxes()}
                  </YStack>
                </ScrollView>
              )}
              <YStack>
                <DropdownDetails
                  title="Medical History"
                  data={
                    consultation?.patient_details_snapshot?.medicalHistory || ""
                  }
                />
              </YStack>
              <YStack>
                <DropdownDetails
                  title="Medications"
                  data={
                    consultation?.patient_details_snapshot?.medications || ""
                  }
                />
              </YStack>
              <YStack>
                <DropdownDetails
                  title="Allergies"
                  data={consultation?.patient_details_snapshot?.allergies || ""}
                />
              </YStack>
              <YStack>
                <PhysicalExam
                  physicalExam={physicalExam}
                  setPhysicalExam={setPhysicalExam}
                  isSubmitted={isSubmitted}
                />
              </YStack>
            </YStack>
          </ScrollView>
        </YStack>
      </YStack>
    </KeyboardAvoidingView>
  );
}

export const useProfileStyles = () => {
  return {
    mainContainer: {
      flex: 1,
    },
    childContainer: {
      flex: 1,
    },
    profileText: {
      fontSize: 20,
      fontWeight: "600" as any,
      marginBottom: 10,
    },
    dottedLineContainer: {
      width: "100%" as "100%",
      alignItems: "flex-start",
      marginBlockStart: 20,
    },
    dottedLine: {
      flexDirection: "row" as "row",
      width: "100%" as "100%",
      height: 2,
      backgroundColor: "transparent" as "transparent",
    },
    dot: {
      width: 5,
      height: 2,
      backgroundColor: "$primaryBorderColor",
      marginRight: 5,
    },
    vitalBoxContainer: {
      width: 90,
      minH: 100,
      borderWidth: 1,
      borderColor: "$borderColor" as any,
      borderBottomLeftRadius: 8,
      borderBottomRightRadius: 8,
      borderTopLeftRadius: 8,
      borderTopRightRadius: 8,
      alignItems: "center" as any,
      justifyContent: "space-between" as any,
      padding: "$2" as any,
    },
    vitalBoxDateAndTimeContainer: {
      fontSize: 10,
      textAlign: "center" as any,
      mt: 6,
      color: "$placeholdertextColor" as any,
    },
  };
};
