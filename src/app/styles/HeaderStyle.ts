// src/styles/HeaderStyles.ts
import { Dimensions } from "react-native";

const { width } = Dimensions.get("window");
const imageWidth = width * 0.5;

export const HeaderStyle = {
  container: {
    justifyContent: "space-between",
    alignItems: "center" as const,
  },
  logo: {
    width: imageWidth,
    height: imageWidth * 0.3,
    objectFit: "contain" as const,
  },
  avatarContainer: {
    circular: true,
    size: "$4" as const,
  },
};
