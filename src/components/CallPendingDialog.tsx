import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Spin<PERSON>, View } from "tamagui";
import { CircleX, UserCheck } from "@tamagui/lucide-icons";
import { useEffect, useState } from "react";
import axiosConfig from "~/services/axiosConfig";
import Constants from "expo-constants";
import { io, Socket } from "socket.io-client";
import { useRouter } from "expo-router";
import { Modal, Platform, Vibration, NativeModules } from "react-native";
import CallKitService from "~/services/CallKitService";

interface CallPendingDialogProps {
  open: boolean;
  onClose: (open: boolean) => void;
  requestData: {
    facilityId: string;
    patientId: string;
    reason: string;
  };
}

interface ConsultationRequest {
  consultationId: string;
  sdkId: string;
}

export function CallPendingDialog({
  open,
  onClose,
  requestData,
}: CallPendingDialogProps): JSX.Element {
  const styles = useCallPendingDialogStyle();
  const { facilityId, patientId, reason } = requestData;
  const [status, setStatus] = useState<string>("pending");
  const router = useRouter();
  const [requestId, setRequestId] = useState<string | null>(null);
  useEffect(() => {
    const socket: Socket = io(Constants.expoConfig?.extra?.apiUrl, {
      transports: ["websocket"],
    });
    socket.on("connect", () => {});

    socket.on("consultationAccepted", (data: ConsultationRequest) => {
      setStatus("accepted");
    });

    socket.on("consultationStarted", async (data: ConsultationRequest) => {
      Vibration.cancel();
      console.log(
        "Canceled vibration in CallPendingDialog consultationStarted"
      );

      if (Platform.OS === "ios") {
        try {
          const activeCalls = await CallKitService.getActiveCalls();
          console.log(
            "Active calls before ending in consultationStarted:",
            activeCalls
          );

          const result = await CallKitService.endAllCalls();
          console.log(
            "Ended all active CallKit calls from consultationStarted:",
            result
          );

          setTimeout(async () => {
            const remainingCalls = await CallKitService.getActiveCalls();
            console.log(
              "Remaining calls after ending in consultationStarted:",
              remainingCalls
            );

            if (remainingCalls.length > 0) {
              console.log(
                "Calls still active, using direct method to end calls"
              );

              if (
                Platform.OS === "ios" &&
                NativeModules.RingtoneModule &&
                typeof NativeModules.RingtoneModule.directEndAllCalls ===
                  "function"
              ) {
                try {
                  const directResult =
                    await NativeModules.RingtoneModule.directEndAllCalls();
                  console.log("Direct end all calls result:", directResult);
                } catch (error) {
                  console.error(
                    "Error using direct method to end calls:",
                    error
                  );
                }
              }
              const resetResult = await CallKitService.resetCallKitProvider();
              console.log("Reset CallKit provider result:", resetResult);
            }
          }, 500);
        } catch (error) {
          console.error(
            "Error ending CallKit calls from consultationStarted:",
            error
          );
        }
      }

      router.push({
        pathname: `/nurse/call`,
        params: { consultationId: data.consultationId, sdkId: data.sdkId },
      });

      onClose(false);
    });

    const sendRequest = async () => {
      try {
        const response = await axiosConfig.post("/consultation/request", {
          facilityId,
          patientId,
          reason,
        });
        setRequestId(response.data.consultationRequest.id);
        socket.emit(
          "joinRoom",
          `consultationRequest:${response.data.consultationRequest.id}`
        );
      } catch (error) {
        console.error("Error sending consultation request:", error);
      }
    };

    sendRequest();

    return () => {
      socket.disconnect();
    };
  }, [facilityId, patientId, reason]);

  const handleCancelRequest = async () => {
    try {
      Vibration.cancel();
      console.log(
        "Canceled vibration in CallPendingDialog handleCancelRequest"
      );

      if (Platform.OS === "ios") {
        try {
          const activeCalls = await CallKitService.getActiveCalls();
          console.log(
            "Active calls before ending in CallPendingDialog:",
            activeCalls
          );
          const result = await CallKitService.endAllCalls();
          console.log(
            "Ended all active CallKit calls from CallPendingDialog:",
            result
          );

          setTimeout(async () => {
            const remainingCalls = await CallKitService.getActiveCalls();
            console.log(
              "Remaining calls after ending in CallPendingDialog:",
              remainingCalls
            );

            if (remainingCalls.length > 0) {
              console.log(
                "Calls still active, using direct method to end calls"
              );

              if (
                Platform.OS === "ios" &&
                NativeModules.RingtoneModule &&
                typeof NativeModules.RingtoneModule.directEndAllCalls ===
                  "function"
              ) {
                try {
                  const directResult =
                    await NativeModules.RingtoneModule.directEndAllCalls();
                  console.log("Direct end all calls result:", directResult);
                } catch (error) {
                  console.error(
                    "Error using direct method to end calls:",
                    error
                  );
                }
              }

              const resetResult = await CallKitService.resetCallKitProvider();
              console.log("Reset CallKit provider result:", resetResult);
            }
          }, 500);
        } catch (error) {
          console.error(
            "Error ending CallKit calls from CallPendingDialog:",
            error
          );
        }
      }

      await axiosConfig.post("/consultation/request/cancel", {
        consultationRequestId: requestId,
      });

      onClose(false);
    } catch (error) {
      console.error("Error cancelling consultation request:", error);
      Vibration.cancel();
      onClose(false);
    }
  };

  return (
    <Modal visible={open} transparent animationType="fade">
      <View
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          flex: 1,
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          height: "100%",
          width: "100%",
        }}
      >
        <Dialog modal open={open} onOpenChange={onClose}>
          <Dialog.Overlay {...styles.overlay} />
          <Dialog.Content {...styles.dialogContent}>
            <YStack {...styles.container}>
              <XStack {...styles.headerContainer}>
                <Text {...styles.headerText}>
                  {status === "pending"
                    ? "Pending Request"
                    : "A Provider is joining the call"}
                </Text>
              </XStack>
              <YStack>
                {status === "pending" ? (
                  <Spinner
                    size="large"
                    color="$loginForgotPasswordColor"
                    {...styles.spinner}
                  />
                ) : (
                  <View {...styles.userIcon}>
                    <UserCheck
                      size={"$7"}
                      strokeWidth={1}
                      color={"$primaryColor"}
                    />
                  </View>
                )}

                <Text {...styles.infoBlockValue}>
                  {status === "pending"
                    ? "Notifying providers"
                    : "A provider has accepted the request and is reviewing the patient details"}
                </Text>
              </YStack>
              {status === "pending" && (
                <Button
                  {...styles.button}
                  icon={<CircleX size={"$1"} />}
                  onPress={() => {
                    handleCancelRequest();
                  }}
                >
                  Cancel Request
                </Button>
              )}
            </YStack>
          </Dialog.Content>
        </Dialog>
      </View>
    </Modal>
  );
}

export const useCallPendingDialogStyle = () => {
  return {
    dialogContent: {
      bordered: true,
      bg: "$screenBackgroundcolor" as any,
      br: "$4" as any,
      shadowColor: "transparent" as any,
      shadowOpacity: 0 as any,
      shadowRadius: 0 as any,
      width: "90%" as any,
      alignSelf: "center" as any,
      justifyContent: "center" as any,
      borderRadius: "$7" as any,
      position: "absolute" as any,
      top: "50%" as any,
      left: "50%" as any,
      transform: [{ translateX: "-50%" }, { translateY: "-50%" }] as any,
      borderColor: "$primaryBorderColor" as any,
    },
    container: {
      gap: "$2" as any,

      marginHor: "$4" as any,
    },
    headerContainer: {
      justifyContent: "space-between",
    },
    headerText: {
      fontSize: 24,
      fontWeight: "600" as any,
    },
    callEndsText: {
      fontSize: 16,
      fontWeight: "200" as any,
    },
    overlay: {
      animation: "quick" as any,
      enterStyle: { opacity: 0 },
      exitStyle: { opacity: 0 },
      backgroundColor: "$callAlertBackground" as any,
      opacity: 0.5,
    },
    infoBlockLabel: {
      fontSize: 18,
      fontWeight: "400" as any,
    },
    infoBlockValue: {
      fontSize: 15,
      fontWeight: "400" as any,
      marginBlockStart: 10,
      textAlign: "center",
    },
    button: {
      color: "$buttonWhiteColor" as any,
      backgroundColor: "$primaryColor" as any,
      marginBlock: 10,
      fontSize: 18,
      fontWeight: 500 as any,
    },
    spinner: {
      marginBlock: 30,
    },
    userIcon: {
      alignItems: "center" as any,
      justiifyContent: "center" as any,
      marginBlock: 20,
    },
  };
};
