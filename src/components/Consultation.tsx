import {
  Ch<PERSON><PERSON>Down,
  <PERSON><PERSON>heck<PERSON>ig,
  FileText,
  MessageSquare,
} from "@tamagui/lucide-icons";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Accordion,
  Button,
  Spinner,
  Square,
  Text,
  View,
  XStack,
  YStack,
} from "tamagui";
import { Badge } from "./Badge";
import { useTheme } from "@/_layout";
import axiosConfig from "~/services/axiosConfig";
import { HorizontalDashedLine } from "./DashedLine";

export const Consultation = ({
  data,
  isFromProvider,
  isFromCallScreen,
  isFromCallOverViewScreen = false,
  onFinishLaterPress,
  onClosePress,
  shouldShowDay = false,
  shouldSowOrderandBadge = true,
}: {
  data: any;
  isFromProvider: boolean;
  isFromCallScreen: boolean;
  isFromCallOverViewScreen?: boolean;
  onFinishLaterPress?: () => void;
  onClosePress?: () => void;
  shouldShowDay?: boolean;
  shouldSowOrderandBadge?: boolean;
}) => {
  const [isOpen, setIsOpen] = useState(
    isFromCallScreen || isFromCallOverViewScreen ? true : false
  );
  const router = useRouter();
  const styles = getStyles();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  const [isOrderConfirmed, setIsOrderConfirmed] = useState(
    data.order_confirmed
  );
  const [day, setDay] = useState<string>("");
  const {
    id,
    full_name,
    dob,
    gender,
    date,
    time,
    order,
    status,
    chief_complaint,
    order_confirmed,
  } = data;

  useEffect(() => {
    // create a midnight-local Date
    const [y, m, d] = date.split("-").map(Number);
    const targetDate = new Date(y, m - 1, d);

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    targetDate.setHours(0, 0, 0, 0);

    const diffInMs = today.getTime() - targetDate.getTime();
    const diffInDays = Math.round(diffInMs / 86400000); // ms in a day

    if (diffInDays >= 0 && diffInDays <= 5) {
      const weekday = targetDate.toLocaleDateString("en-US", {
        weekday: "short",
      });
      setDay(weekday);
    } else {
      const formatted = [
        String(targetDate.getMonth() + 1).padStart(2, "0"),
        String(targetDate.getDate()).padStart(2, "0"),
      ].join("/");
      setDay(formatted);
    }
  }, [date]);

  useEffect(() => {
    setIsOpen(isFromCallScreen || isFromCallOverViewScreen ? true : false);
  }, [isFromCallScreen, isFromCallOverViewScreen]);

  if (!data) return null;

  const onViewChatPress = () => {
    router.push({ pathname: "/nurse/chat", params: { consultationId: id } });
  };
  const onReviewCallPress = () => {
    router.push({
      pathname: "/provider/reviewcall",
      params: { consultationId: id },
    });
  };
  const hasOrder = order && order !== "Waiting for Provider to place order";

  const onConfirmOrder = async () => {
    const response = await axiosConfig.put(`/consultation/${id}/order/confirm`);
    if (response.status === 200) {
      setIsOrderConfirmed(true);
    }
    if (response.status === 400) {
      setIsOrderConfirmed(false);
    }
  };

  return (
    <View {...styles.container}>
      <Accordion
        overflow="hidden"
        borderWidth={1}
        type="multiple"
        {...styles.accordion}
        borderColor={isDarkMode ? "#697586" : "#D0D5DD"}
        value={isOpen ? ["a1"] : []}
      >
        <Accordion.Item value="a1">
          <Accordion.Trigger
            {...styles.trigger}
            onPress={() => setIsOpen(!isOpen)}
          >
            {({ open }: { open: boolean }) => (
              <XStack {...styles.headerRow}>
                <YStack>
                  <XStack {...styles.nameContainer}>
                    <XStack maxW={210} marginInlineEnd={5}>
                      {shouldShowDay && (
                        <>
                          <Text {...styles.dayText}>{day}</Text>
                          <View {...styles.verticalIndicator} />
                        </>
                      )}
                      <Text
                        style={{
                          flexShrink: 1,
                          flexWrap: "wrap",
                        }}
                        {...styles.nameText}
                      >
                        {full_name}
                      </Text>
                    </XStack>

                    {!isFromCallOverViewScreen && shouldSowOrderandBadge && (
                      <View>
                        <Badge status={status} />
                      </View>
                    )}
                  </XStack>

                  <XStack {...styles.detailsRow}>
                    <Text {...styles.detailText}>Gender: {gender}</Text>
                    <Text {...styles.separator}>|</Text>
                    <Text {...styles.detailText}>DOB: {dob}</Text>
                  </XStack>
                </YStack>
                <Square animation="quick" rotate={open ? "180deg" : "0deg"}>
                  <ChevronDown size="$1" />
                </Square>
              </XStack>
            )}
          </Accordion.Trigger>
          <Accordion.HeightAnimator animation="medium">
            <Accordion.Content {...styles.content}>
              <YStack {...styles.innerContainer}>
                <View {...styles.divider} />
                <YStack>
                  <YStack {...styles.section}>
                    <Text {...styles.sectionTitle}>Date:</Text>
                    <Text {...styles.sectionText}>
                      {time}, {date}
                    </Text>
                  </YStack>
                  <YStack {...styles.section}>
                    <Text {...styles.sectionTitle}>Chief Complaint:</Text>
                    <Text {...styles.sectionText}>{chief_complaint}</Text>
                  </YStack>
                  {shouldSowOrderandBadge && (
                    <YStack {...styles.section}>
                      <Text {...styles.sectionTitle}>Order:</Text>
                      <Text {...styles.sectionText}>
                        {!order && (
                          <Spinner
                            size="small"
                            color={isDarkMode ? "#697586" : "#D0D5DD"}
                            style={{
                              display: hasOrder ? "none" : "flex",
                              marginInlineEnd: 5,
                            }}
                          />
                        )}
                        {order
                          ? order
                          : "Order Pending - Waiting for Provider to place order"}
                      </Text>
                    </YStack>
                  )}
                </YStack>
                {!isFromCallScreen && (
                  <YStack marginBlockStart={15}>
                    <View {...styles.divider} />
                  </YStack>
                )}
                {!isFromCallScreen && (
                  <YStack {...styles.buttonContainer}>
                    {!isFromProvider ? (
                      <>
                        <Button
                          {...styles.viewTranscriptButton}
                          icon={<MessageSquare size={"$1"} />}
                          onPress={onViewChatPress}
                        >
                          View chat
                        </Button>
                        {!isOrderConfirmed && (
                          <Button
                            disabled={!hasOrder}
                            {...styles.confirmOrderButton}
                            style={!hasOrder ? styles.iconDisabled : {}}
                            icon={
                              <CircleCheckBig
                                size={"$1"}
                                color={
                                  hasOrder
                                    ? "$confirmOrderTextColor"
                                    : "#D0D5DD"
                                }
                              />
                            }
                            onPress={() => onConfirmOrder()}
                          >
                            Confirm Order
                          </Button>
                        )}
                        {isOrderConfirmed && (
                          <Button
                            disabled={true}
                            {...styles.orderConfirmedBtn}
                            icon={<CircleCheckBig size={"$1"} />}
                          >
                            Order Confirmed
                          </Button>
                        )}
                        {isFromCallOverViewScreen && (
                          <Button
                            {...styles.confirmOrderButton}
                            onPress={
                              !isOrderConfirmed
                                ? onFinishLaterPress
                                : onClosePress
                            }
                          >
                            {isOrderConfirmed ? "Close" : "Finish Later"}
                          </Button>
                        )}
                      </>
                    ) : (
                      <Button
                        {...styles.reviewCallBtn}
                        icon={<FileText size={"$1"} />}
                        onPress={onReviewCallPress}
                      >
                        Review Call
                      </Button>
                    )}
                  </YStack>
                )}
              </YStack>
            </Accordion.Content>
          </Accordion.HeightAnimator>
        </Accordion.Item>
      </Accordion>
      {!isFromCallOverViewScreen && !isFromCallScreen && isOpen && (
        <HorizontalDashedLine
          height={1}
          dashLength={2}
          dashGap={2}
          color="#D2D6DB"
          style={{ marginTop: 18 }}
        />
      )}
    </View>
  );
};

const getStyles = () => {
  return {
    container: {
      marginBlockStart: 20,
    },
    accordion: {
      borderRadius: "$6" as "$6",
    },
    trigger: {
      flexDirection: "row" as "row",
      borderWidth: 0,
      backgroundColor: "$screenBackgroundcolor",
    },
    headerRow: {
      justifyContent: "space-between",
      flex: 1,
    },
    nameText: {
      fontSize: 16,
      fontWeight: "600" as any,
      marginInlineEnd: 10,
      color: "$textcolor" as any,
      maxWidth: 220,
    },
    detailsRow: {
      marginBlockStart: 5,
    },
    detailText: {
      fontSize: 14,
      fontWeight: "400" as any,
      marginInlineEnd: 10,
      color: "$textcolor" as any,
    },
    separator: {
      fontSize: 16,
      fontWeight: "200" as "200",
      marginInlineEnd: 10,
    },
    content: {
      animation: "medium" as "medium",
      exitStyle: { opacity: 0 },
      backgroundColor: "$screenBackgroundcolor",
    },
    innerContainer: {
      marginBlockStart: -20,
    },
    divider: {
      height: 1,
      backgroundColor: "$primaryBorderColor",
    },
    section: {
      marginBlockStart: 15,
    },
    sectionTitle: {
      fontSize: 14,
      fontWeight: "500" as any,
      color: "$textcolor" as any,
    },
    sectionText: {
      fontSize: 14,
      fontWeight: "400" as any,
      marginBlockStart: 7,
      color: "$textcolor" as any,
    },
    buttonContainer: {
      marginBlockStart: 20,
    },
    viewTranscriptButton: {
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      backgroundColor: "$screenBackgroundcolor",
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
    },
    iconDisabled: {
      color: "#D0D5DD",
    },
    confirmOrderButton: {
      marginBlockStart: 10,
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      backgroundColor: "$confirmOrderBlue",
      borderColor: "$confirmOrderBorderCOlor" as any,
      color: "$confirmOrderTextColor" as any,
      borderWidth: 1,
    },
    orderConfirmedBtn: {
      marginBlockStart: 10,
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      backgroundColor: "$orderConfirmedBackground",
      borderColor: "$primaryBorderColor" as any,
      color: "$orderConfirmedTextColor" as any,
      borderWidth: 1,
    },
    reviewCallBtn: {
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      backgroundColor: "$confirmOrderBlue",
      borderColor: "$confirmOrderBorderCOlor" as any,
      color: "$confirmOrderTextColor" as any,
      borderWidth: 1,
    },
    dottedLineContainer: {
      width: "100%" as "100%",
      alignItems: "flex-start",
      marginBlockStart: 20,
    },
    dottedLine: {
      flexDirection: "row" as "row",
      width: "100%" as "100%",
      height: 2,
      backgroundColor: "transparent" as "transparent",
    },
    dot: {
      width: 5,
      height: 2,
      backgroundColor: "$primaryBorderColor",
      marginRight: 5,
    },
    verticalIndicator: {
      width: 1,
      height: 16,
      backgroundColor: "$textcolor",
      marginInline: 5,
    },
    dayText: {
      fontSize: 16,
      fontWeight: "600" as any,
      color: "$textcolor" as any,
    },
    nameContainer: {
      alignItems: "center" as any,
      flexWrap: "wrap" as any,
    },
  };
};

export default Consultation;
