import { useTheme } from "@/_layout";
import { ChevronDown } from "@tamagui/lucide-icons";
import { useState, useEffect } from "react";
import {
  Accordion,
  Square,
  Text,
  TextArea,
  View,
  XStack,
  YStack,
} from "tamagui";

interface DropdownTextEditorProps {
  title: "Orders";
  onChangeText?: (text: string) => void;
  data: string;
  isComingFromReviewCall?: boolean;
  isSubmitted?: boolean;
  orderConfirmed?: boolean;
}

export default function DropdownTextEditor({
  title,
  onChangeText,
  data,
  isComingFromReviewCall = false,
  isSubmitted = false,
  orderConfirmed = false,
}: DropdownTextEditorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const styles = getStyles();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";

  useEffect(() => {
    setInputValue(data);
  }, [data]);

  const handleTextChange = (text: string) => {
    setInputValue(text);
    onChangeText?.(text);
  };

  return (
    <View {...styles.container}>
      <Accordion
        overflow="hidden"
        borderWidth={1}
        type="multiple"
        {...styles.accordion}
        borderColor={isDarkMode ? "#697586" : "#D0D5DD"}
      >
        <Accordion.Item value="a1">
          <Accordion.Trigger
            {...styles.trigger}
            onPress={() => setIsOpen(!isOpen)}
          >
            {({ open }: { open: boolean }) => (
              <XStack {...styles.headerRow}>
                <YStack {...styles.titleTextarea}>
                  <XStack>
                    <Text {...styles.headerText}>{title}</Text>
                    {title === "Orders" && orderConfirmed && (
                      <Text
                        {...styles.orderConfirmText}
                        style={{
                          flexWrap: "wrap",
                          flexShrink: 1,
                        }}
                      >
                        The order has been confirmed by the recipient and can no
                        longer be edited.
                      </Text>
                    )}
                  </XStack>
                </YStack>
                <Square animation="quick" rotate={open ? "180deg" : "0deg"}>
                  <ChevronDown size="$1" />
                </Square>
              </XStack>
            )}
          </Accordion.Trigger>
          <Accordion.HeightAnimator animation="medium">
            <Accordion.Content {...styles.content}>
              <YStack {...styles.innerContainer}>
                <TextArea
                  {...styles.inputText}
                  value={inputValue ?? data}
                  onChangeText={handleTextChange}
                  placeholder={
                    title !== "Orders" ? "N/A" : "Please enter the details."
                  }
                  placeholderTextColor={"$textcolor"}
                  overflow="hidden"
                  editable={!isSubmitted && !orderConfirmed}
                />
              </YStack>
            </Accordion.Content>
          </Accordion.HeightAnimator>
        </Accordion.Item>
      </Accordion>
    </View>
  );
}
const getStyles = () => {
  return {
    container: {
      marginBlockStart: 20,
    },
    accordion: {
      borderRadius: "$6" as "$6",
    },
    trigger: {
      flexDirection: "row" as "row",
      borderWidth: 0,
      backgroundColor: "$screenBackgroundcolor",
    },
    headerRow: {
      justifyContent: "space-between",
      flex: 1,
    },
    content: {
      animation: "medium" as "medium",
      exitStyle: { opacity: 0 },
      backgroundColor: "$screenBackgroundcolor",
    },
    innerContainer: {
      marginBlockStart: -30,
      marginInlineStart: -15,
    },
    headerText: {
      fontSize: 16,
      fontWeight: 600 as any,
    },
    inputText: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderWidth: 0,
      color: "$textcolor" as any,
      fontWeight: 300 as 300,
      fontSize: 14,
      textAlignVertical: "top" as any,
    },
    orderConfirmText: {
      color: "$textcolor" as any,
      fontWeight: 300 as 300,
      fontSize: 14,
      marginInline: 10,
      maxWidth: "100%",
    },
    titleTextarea: {
      maxWidth: "90%" as any,
    },
  };
};
