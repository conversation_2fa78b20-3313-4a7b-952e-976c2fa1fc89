import {
  Mail,
  PhoneCall,
  Plus,
  Save,
  CheckCircle,
  Check,
} from "@tamagui/lucide-icons";
import { useState } from "react";
import { Text, YStack, Button, View, Input, XStack, Checkbox } from "tamagui";
import ContactDropDown from "./ContactDropDown";
import { useNotificationStyle } from "./componentstyles/NotificationStyle";
import { useAuth } from "~/context/AuthContext";
import { Badge } from "./Badge";
import Title from "./Title";

export default function Notifications() {
  const { localUser, updatePhone } = useAuth();

  const [contactList, setContactList] = useState<
    { type: string; value: string; isDefault: boolean }[]
  >(() => {
    const contacts = [];
    if (localUser?.email)
      contacts.push({ type: "email", value: localUser.email, isDefault: true });
    if (localUser?.phoneNumber) {
      contacts.push({
        type: "mobile",
        value: localUser.phoneNumber,
        isDefault: !localUser.email, // Default only if no email exists
      });
    }
    return contacts;
  });
  const [selectedOption, setSelectedOption] = useState<string>("mobile");
  const [inputValue, setInputValue] = useState<string>("");
  const [isAddingContact, setIsAddingContact] = useState<boolean>(false);
  const Style = useNotificationStyle();
  const [isChecked, setIsChecked] = useState(false);
  console.log("Contact List: ", contactList);

  const handleSaveContact = async () => {
    if (!inputValue.trim()) return; // Prevent adding empty contacts

    if (!isChecked) {
      alert("Please check the consent box before proceeding.");
      return;
    }

    if (selectedOption === "mobile") {
      // Validate phone number
      const phoneRegex = /^\d{10}$/; // Ensures exactly 10 digits
      if (!phoneRegex.test(inputValue)) {
        alert("Please enter a valid 10-digit mobile number.");
        return;
      }

      await updatePhone(inputValue);

      // Rebuild the contact list from the latest user data
      setContactList(() => {
        const updatedContacts = [];
        if (localUser?.email) {
          updatedContacts.push({
            type: "email",
            value: localUser.email,
            isDefault: true,
          });
        }
        updatedContacts.push({
          type: "mobile",
          value: inputValue,
          isDefault: !localUser?.email, // Default only if no email exists
        });

        return updatedContacts;
      });
    }
    setIsChecked(false);
    setInputValue("");
    setIsAddingContact(false);
  };

  return (
    <YStack {...Style.card}>
      <YStack {...Style.mainStack}>
        <Text {...Style.notificationText}>Notifications</Text>

        {/* Show contact list only if isAddingContact is false */}
        {!isAddingContact && (
          <View>
            <YStack>
              {contactList.length > 0 ? (
                contactList.map((contact, index) => (
                  <YStack key={index} {...Style.contactItem}>
                    <XStack {...Style.contactRow}>
                      <XStack {...Style.innerContactRow}>
                        {contact.type === "email" ? (
                          <Mail size={"$1"} />
                        ) : (
                          <PhoneCall size={"$1"} />
                        )}
                        <XStack {...Style.emailText}>
                          <Text>{contact.value}</Text>
                          {/* {!contact.isDefault && (
                            <Text
                              {...Style.removeText}
                              onPress={() => {
                                // show modal to confirm removal
                              }}
                            >
                              {" "}
                              Remove{" "}
                            </Text>
                          )} */}
                        </XStack>
                      </XStack>
                      <XStack>
                        {contact.isDefault && (
                          <Title
                            text="Default"
                            backgroundColor="#F8F9FC"
                            borderColor="#D5D9EB"
                          />
                        )}
                      </XStack>
                    </XStack>
                  </YStack>
                ))
              ) : (
                <YStack {...Style.noContacts}>
                  <PhoneCall size={"$3"} />
                  <Text {...Style.addContactText}>
                    Add an email or phone number.
                  </Text>
                </YStack>
              )}
            </YStack>

            {/* Add Button - Shows Add Contact Drawer */}
            {/* TODO: add back - removed for apple submit */}
            {/* <YStack>
              <Button
                icon={<Plus size={"$1"} />}
                {...Style.btnAdd}
                onPress={() => setIsAddingContact(true)}
              >
                Add
              </Button>
            </YStack> */}
          </View>
        )}

        {/* Add Contact Drawer - Visible only when isAddingContact is true */}
        {isAddingContact && (
          <View>
            <YStack {...Style.addContactDrawer}>
              <ContactDropDown
                onSelect={(value) => setSelectedOption(value)}
                selectedOption={selectedOption}
              />
              <YStack {...Style.contactDetails}>
                <Text {...Style.titleText}>
                  {selectedOption === "email" ? "Name" : "Number"}
                </Text>
                <Input
                  {...Style.contactDetailsInput}
                  placeholder={
                    selectedOption === "email"
                      ? "Enter Email"
                      : "Enter Mobile Number"
                  }
                  keyboardType={
                    selectedOption === "email" ? "email-address" : "phone-pad"
                  }
                  placeholderTextColor={"$textcolor"}
                  value={inputValue}
                  onChangeText={setInputValue}
                />
                <XStack {...Style.consent}>
                  <Checkbox
                    {...Style.checkbox}
                    marginRight={10}
                    marginTop={3}
                    checked={isChecked}
                    onCheckedChange={(checked) =>
                      setIsChecked(checked === true)
                    }
                  >
                    <Checkbox.Indicator>
                      <Check />
                    </Checkbox.Indicator>
                  </Checkbox>
                  <Text flex={1} flexWrap="wrap">
                    I consent to receive text message notifications regarding
                    new patient consultation requests from VitalCare. I have
                    read and agree to the [Privacy Policy] and [Terms of
                    Service]. Standard message and data rates may apply.
                  </Text>
                </XStack>

                {/* Save Button - Adds contact and hides the drawer */}
                <Button
                  icon={<Save size={"$1"} />}
                  {...Style.saveBtn}
                  onPress={handleSaveContact}
                >
                  Save
                </Button>
                <Button
                  // icon={<Save size={"$1"} />}
                  {...Style.closeBtn}
                  onPress={() => {
                    setIsChecked(false);
                    setInputValue("");
                    setIsAddingContact(false);
                  }}
                >
                  Close
                </Button>
              </YStack>
            </YStack>
          </View>
        )}
      </YStack>
    </YStack>
  );
}
