import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  MutableRefObject,
} from "react";
import {
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  StyleSheet,
  View,
  Keyboard,
  ActivityIndicator,
} from "react-native";
import { YStack, Square } from "tamagui";
import { ChevronDown } from "@tamagui/lucide-icons";

export interface SelectableItem {
  id: string;
  name: string;
  dob: string;
}

interface PatientSearchDrawerProps {
  data: SelectableItem[] | undefined;
  placeholder?: string;
  onSelect: (id: string) => void;
  onOpen?: () => void;
  onClose?: () => void;
  isOpen?: boolean;
  onSearch: (query: string) => Promise<void>;
  loading?: boolean;
  error?: string;
  disabled?: boolean;
}

const PatientSearchDrawer: React.FC<PatientSearchDrawerProps> = ({
  data = [],
  placeholder = "Select a Patient",
  onSelect,
  onOpen,
  onClose,
  isOpen = false,
  onSearch,
  loading = false,
  error = "",
  disabled = false,
}) => {
  const [search, setSearch] = useState<string>("");
  const [clicked, setClicked] = useState<boolean>(false);
  const [selectedPatient, setSelectedPatient] = useState<string>("");
  const searchRef = useRef<TextInput>(null);

  const debounceRef = useRef<number | null>(null) as MutableRefObject<
    number | null
  >;

  useEffect(() => {
    setClicked(isOpen);
  }, [isOpen]);

  const handleOpen = () => {
    if (!clicked) {
      setClicked(true);
      onOpen?.();
    }
  };

  const handleClose = () => {
    setClicked(false);
    onClose?.();
  };

  const searchPatients = (query: string) => {
    if (debounceRef.current !== null) {
      clearTimeout(debounceRef.current);
    }
    debounceRef.current = window.setTimeout(() => {
      if (query.length >= 3) {
        setSearch(query);
        onSearch(query);
      } else {
        setSearch("");
      }
    }, 500);
  };

  const handleSelect = useCallback(
    (item: SelectableItem) => {
      onSelect(item.id);
      setSelectedPatient(item.name);
      handleClose();
      setSearch("");
      Keyboard.dismiss();
    },
    [onSelect]
  );

  return (
    <View style={styles.container}>
      {
        <TouchableOpacity
          disabled={disabled}
          style={disabled ? styles.disabledSelectButton : styles.selectButton}
          onPress={() => {
            clicked ? handleClose() : handleOpen();
            Keyboard.dismiss();
          }}
        >
          <Text
            style={disabled ? styles.disabledSelectedText : styles.selectedText}
          >
            {selectedPatient === "" ? placeholder : selectedPatient}
          </Text>
          <Square animation="quick" rotate={clicked ? "180deg" : "0deg"}>
            <ChevronDown size={20} color="black" />
          </Square>
        </TouchableOpacity>
      }

      {clicked && (
        <YStack style={styles.dropdownContainer}>
          <YStack style={styles.dropdownContainerChild}>
            <TextInput
              placeholder="Search by name..."
              autoCorrect={false}
              ref={searchRef}
              onChangeText={searchPatients}
              style={styles.searchInput}
            />
            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color="#000" />
                <Text style={styles.loadingText}>Loading patients...</Text>
              </View>
            ) : error ? (
              <Text style={styles.errorText}>{error}</Text>
            ) : (
              <FlatList
                data={data}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={styles.listItem}
                    onPress={() => handleSelect(item)}
                  >
                    <Text style={styles.listItemText}>
                      {item.name}
                      {item.dob
                        ? ` (${item.dob})`
                        : item.id
                          ? ` (ID ${item.id})`
                          : ""}
                    </Text>
                  </TouchableOpacity>
                )}
                keyboardShouldPersistTaps="handled"
                ListEmptyComponent={() => (
                  <Text style={styles.emptyText}>
                    {search.length >= 3
                      ? "No results found"
                      : "Type at least 3 letters to search"}
                  </Text>
                )}
              />
            )}
          </YStack>
        </YStack>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    position: "relative",
  },
  selectButton: {
    width: "100%",
    height: 50,
    borderRadius: 10,
    borderWidth: 0.5,
    borderColor: "#D0D5DD",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 15,
    backgroundColor: "white",
  },
  disabledSelectButton: {
    width: "100%",
    height: 50,
    borderRadius: 10,
    borderWidth: 0.5,
    borderColor: "#D0D5DD",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 15,
    // backgroundColor: "grey",
  },
  disabledSelectedText: {
    fontWeight: "500",
    fontSize: 16,
    color: "#D0D5DD",
  },
  selectedText: {
    fontWeight: "500",
    fontSize: 16,
  },
  dropdownContainer: {
    position: "absolute",
    top: 60,
    left: 0,
    right: 0,
    zIndex: 999,
    maxHeight: 250,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  dropdownContainerChild: {
    borderWidth: 1,
    borderColor: "#D0D5DD",
    borderRadius: 8,
    backgroundColor: "white",
  },
  searchInput: {
    height: 50,
    borderWidth: 0.5,
    borderColor: "#D0D5DD",
    paddingHorizontal: 15,
    borderRadius: 7,
    backgroundColor: "white",
    margin: 15,
  },
  listItem: {
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  listItemText: {
    fontWeight: "600",
  },
  emptyText: {
    textAlign: "center",
    padding: 10,
    fontSize: 16,
    color: "#888",
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  loadingText: {
    marginLeft: 10,
    fontSize: 16,
    color: "#000",
  },
  errorText: {
    textAlign: "center",
    padding: 10,
    fontSize: 16,
    color: "red",
  },
});

export default PatientSearchDrawer;
