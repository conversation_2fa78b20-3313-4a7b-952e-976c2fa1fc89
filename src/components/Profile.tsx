import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Text, YStack } from "tamagui";
import { memo, useState } from "react";
import Title from "./Title";
import EditProfile from "./EditProfile";
import FacilityDrawer from "./FacilityDrawer";
import { useAuth } from "~/context/AuthContext";

export default function Profile() {
  return <ProfileSection />;
}

const ProfileSection = memo(() => {
  const { localUser } = useAuth();
  const initials = localUser
    ? localUser?.firstName?.charAt(0) + localUser?.lastName?.charAt(0)
    : "User";
  const [isEditing, setIsEditing] = useState(false);
  const facilityData = [
    { name: "Hospital A", id: "1" },
    { name: "Clinic B", id: "2" },
    { name: "Medical Center C", id: "3" },
  ];
  return (
    <YStack {...styles.profileContainer}>
      <Card {...styles.profileCard}>
        <YStack {...styles.profileContent}>
          <Avatar circular size="$6">
            <Avatar.Fallback
              style={{
                backgroundColor: "#1570EF",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text style={{ color: "white" }}>{initials}</Text>
            </Avatar.Fallback>
          </Avatar>
          <Text {...styles.profileText}>
            {localUser?.firstName + " " + localUser?.lastName}
          </Text>
          {localUser?.role !== "nurse" && (
            <Text {...styles.emailText}>{localUser?.email}</Text>
          )}
          <YStack {...styles.badge}>
            <Title
              text={localUser?.role === "nurse" ? "Nurse" : "Provider"} // TODO: pull from database once implemented
              backgroundColor="#F8F9FC"
              borderColor="#D5D9EB"
            />
          </YStack>
          {!isEditing && (
            <Button {...styles.editButton} onPress={() => setIsEditing(true)}>
              Edit Profile
            </Button>
          )}
        </YStack>
        {isEditing && (
          <YStack marginInline={20} marginBlock={5}>
            <EditProfile onClose={() => setIsEditing(false)} />
          </YStack>
        )}
        {/* <Text {...styles.separator} />
        <YStack {...styles.facilityContainer}>
          <Text {...styles.facilityTitle}>FACILITY</Text>
          <FacilityDrawer
            data={facilityData}
            placeholder="Select a Facility"
            onSelect={() => {}}
          />
        </YStack> */}
      </Card>
    </YStack>
  );
});

const styles = {
  badge: {
    marginBlockStart: 10,
  },
  separator: {
    height: 1,
    backgroundColor: "$primaryBorderColor",
  },
  profileContainer: {
    borderRadius: "$5" as "$5",
    backgroundColor: "transparent",
    borderColor: "$primaryBorderColor" as any,
    borderWidth: 1,
  },
  profileCard: {
    paddingBlock: 0,
    backgroundColor: "transparent" as "transparent",
  },
  profileContent: {
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  profileText: {
    fontWeight: 500 as any,
    fontSize: 14,
    marginBlock: 10,
  },
  emailText: {
    fontWeight: 400 as any,
    fontSize: 14,
  },
  editButton: {
    backgroundColor: "$screenBackgroundcolor" as any,
    borderWidth: 1,
    borderColor: "$primaryBorderColor" as any,
    paddingInline: 25,
    marginBlock: 10,
    fontWeight: 600 as any,
    fontSize: 14,
    size: "$4" as any,
  },
  facilityContainer: {
    marginBlock: 20,
    marginInline: 20,
  },
  facilityTitle: {
    fontWeight: 600 as any,
    fontSize: 14,
    marginBlockEnd: 10,
  },
};
