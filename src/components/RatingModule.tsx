import React, { useState, useEffect } from "react";
import { Star } from "@tamagui/lucide-icons";
import { Text, View, XStack } from "tamagui";

type RatingModuleProps = {
  title: string;
  onRatingChange: (value: number) => void;
  isEditable: boolean;
  rating: number;
};

const RatingModule = ({
  title,
  onRatingChange,
  isEditable,
  rating,
}: RatingModuleProps) => {
  const [selectedRating, setSelectedRating] = useState(rating);

  // Sync internal state with parent's rating prop
  useEffect(() => {
    setSelectedRating(rating);
  }, [rating]);

  const handleRatingPress = (rating: number) => {
    if (selectedRating === rating) {
      setSelectedRating(0);
      onRatingChange(0);
    } else {
      setSelectedRating(rating);
      onRatingChange(rating);
    }
  };

  return (
    <View {...ratingStyles.container}>
      <Text {...ratingStyles.title}>{title}</Text>

      <XStack {...ratingStyles.starsContainer}>
        {[1, 2, 3, 4, 5].map((rating) => (
          <Star
            key={rating}
            size={"$5"}
            marginInlineEnd={15}
            color={"#175CD3"}
            strokeWidth={1}
            fill={selectedRating >= rating ? "#175CD3" : "transparent"}
            onPress={() => handleRatingPress(rating)}
            disabled={!isEditable}
          />
        ))}
      </XStack>
      <View {...ratingStyles.divider}></View>
    </View>
  );
};

export default RatingModule;

const ratingStyles = {
  container: {
    marginBlockStart: 20,
  },
  title: {
    fontSize: 14,
    fontWeight: 500 as any,
    color: "$textcolor" as any,
  },
  divider: {
    height: 1,
    backgroundColor: "$primaryBorderColor",
  },
  starsContainer: {
    marginBlock: 15,
  },
};
