import { ArrowLeft } from "@tamagui/lucide-icons";
import { Button, Text, XStack, YStack } from "tamagui";

interface HeaderProps {
  onBackPress?: () => void;
  screenName: string;
  isFromCallScreen: boolean;
}

export default function ReviewCallHeader({
  screenName,
  onBackPress,
  isFromCallScreen,
}: HeaderProps) {
  return (
    <XStack {...stackStyles.container}>
      <YStack>
        {/* Back Button and Screen Name */}
        <XStack {...stackStyles.leftSection}>
          {isFromCallScreen && (
            <Button
              {...buttonStyles.backButton}
              icon={ArrowLeft}
              onPress={onBackPress}
            />
          )}
          <Text {...textStyles.screenName}>{screenName}</Text>
        </XStack>
        {/* {callended && <Text {...textStyles.callEndedText}>CALL ENDED</Text>} */}
      </YStack>
    </XStack>
  );
}

// Styles as objects
const stackStyles = {
  container: {
    justifyContent: "space-between",
    alignItems: "center",
  },
  leftSection: {
    alignItems: "center",
    gap: 10,
  },
};

const buttonStyles = {
  backButton: {
    height: 40,
    width: 40,
    borderRadius: 7,
    borderWidth: 1,
    backgroundColor: "$screenBackgroundcolor",
    borderColor: "$primaryBorderColor" as any,
  },
};

const textStyles = {
  screenName: {
    fontSize: 18,
    fontWeight: "500" as "500",
  },
  callEndedText: {
    fontSize: 14,
    fontWeight: "400" as "400",
  },
};
