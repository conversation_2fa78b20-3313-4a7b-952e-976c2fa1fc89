import { ArrowLeft } from "@tamagui/lucide-icons";
import { Pressable } from "react-native";
import { Avatar, Button, Text, XStack } from "tamagui";
import { useAuth } from "~/context/AuthContext";

interface HeaderProps {
  onAvatarPress?: () => void;
  onBackPress?: () => void;
  screenName: string;
}

export default function ScreenHeader({
  onAvatarPress,
  screenName,
  onBackPress,
}: HeaderProps) {
  const { user } = useAuth();
  const initials = user
    ? user?.firstName?.charAt(0) + user?.lastName?.charAt(0)
    : "User";
  return (
    <XStack {...stackStyles.container}>
      {/* Back Button and Screen Name */}
      <XStack {...stackStyles.leftSection}>
        <Button
          {...buttonStyles.backButton}
          icon={ArrowLeft}
          onPress={onBackPress}
        />
        <Text {...textStyles.screenName}>{screenName}</Text>
      </XStack>

      {/* Profile Avatar */}
      <Pressable onPress={onAvatarPress}>
        <Avatar {...avatarStyles.container}>
          <Avatar.Fallback
            style={{
              backgroundColor: "#1570EF",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text style={{ color: "white" }}>{initials}</Text>
          </Avatar.Fallback>
        </Avatar>
      </Pressable>
    </XStack>
  );
}

// Styles as objects
const stackStyles = {
  container: {
    justifyContent: "space-between",
    alignItems: "center",
  },
  leftSection: {
    alignItems: "center",
    gap: 10,
  },
};

const buttonStyles = {
  backButton: {
    height: 40,
    width: 40,
    borderRadius: 7,
    borderWidth: 1,
    backgroundColor: "$screenBackgroundcolor",
    borderColor: "$primaryBorderColor" as any,
  },
};

const textStyles = {
  screenName: {
    fontSize: 20,
    fontWeight: "600" as any,
  },
};

const avatarStyles = {
  container: {
    circular: true,
    size: "$4" as "$4",
    borderColor: "$primaryBorderColor" as any,
  },
  fallback: {
    backgroundColor: "$screenBackgroundcolor" as any,
  },
};
