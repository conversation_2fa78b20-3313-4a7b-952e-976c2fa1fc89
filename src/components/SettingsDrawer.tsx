import {
  Briefcase,
  <PERSON><PERSON>el<PERSON>,
  FileText,
  LogOut,
  Settings,
} from "@tamagui/lucide-icons";
import { Sheet } from "@tamagui/sheet";
import React, { memo, useState } from "react";
import { <PERSON><PERSON>, Linking, ScrollView } from "react-native";
import { Avatar, Button, Card, Text, YStack } from "tamagui";
import { useAuth } from "../context/AuthContext";
import FacilityDrawer from "./FacilityDrawer";
import SettingsDrawerButton from "./SettingsDrawerButton";
import { useRouter } from "expo-router";
import EditProfile from "./EditProfile";

interface SheetDemoProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

interface User {
  token: string;
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: "nurse" | "provider";
  isOnline?: boolean;
}

const SheetDemo = memo(({ open, setOpen }: SheetDemoProps) => {
  const styles = useStyles();
  const spModes = ["percent", "constant", "fit", "mixed"] as const;
  const [position, setPosition] = useState(0);
  const [snapPointsMode, setSnapPointsMode] =
    useState<(typeof spModes)[number]>("percent");
  const { localUser } = useAuth();

  const snapPoints =
    snapPointsMode === "percent"
      ? [83]
      : snapPointsMode === "constant"
        ? [256, 190]
        : snapPointsMode === "fit"
          ? undefined
          : ["80%", 256, 190];
  return (
    <YStack>
      <Sheet
        modal
        open={open}
        onOpenChange={setOpen}
        snapPoints={snapPoints}
        snapPointsMode={snapPointsMode}
        dismissOnSnapToBottom
        position={position}
        onPositionChange={setPosition}
        zIndex={100_000}
        animation="quickest"
      >
        <Sheet.Overlay
          animation="quickest"
          {...styles.sheetOverlay}
          enterStyle={{ opacity: 0 }}
          exitStyle={{ opacity: 0 }}
        />

        <Sheet.Handle />
        <Sheet.Frame {...styles.sheetFrame}>
          <ScrollView>
            <SheetContents setOpen={setOpen} user={localUser} />
          </ScrollView>
        </Sheet.Frame>
      </Sheet>
    </YStack>
  );
});

const SettingsSection = memo(
  ({
    setOpen,
    user,
  }: {
    setOpen: (open: boolean) => void;
    user: User | null;
  }) => {
    const router = useRouter();
    const { signIn, signOut } = useAuth();
    const styles = useStyles();
    const onSettingsPress = (
      setOpen: (open: boolean) => void,
      router: ReturnType<typeof useRouter>
    ) => {
      setOpen(false);
      setTimeout(() => {
        if (user?.role === "nurse") router.push("/nurse/settings");
        else {
          router.push("/provider/settings");
        }
      }, 100);
    };

    const handleHelpPress = () => {
      const url = "https://www.vitalcare.org/contact";
      Linking.openURL(url).catch((err) =>
        console.error("Error opening URL:", err)
      );
    };

    const handlePrivacyPress = () => {
      const url = "https://www.vitalcare.org/privacy-policy";
      Linking.openURL(url).catch((err) =>
        console.error("Error opening URL:", err)
      );
    };

    const handleTermsPress = () => {
      const url = "https://www.vitalcare.org/terms-of-service-2";
      Linking.openURL(url).catch((err) =>
        console.error("Error opening URL:", err)
      );
    };

    return (
      <YStack {...styles.sectionContainer}>
        <SettingsDrawerButton
          name="Settings"
          icon={Settings}
          action={() => onSettingsPress(setOpen, router)}
        />
        <Text {...styles.separator} />
        <SettingsDrawerButton
          name="Help"
          icon={CircleHelp}
          action={() => {
            handleHelpPress();
          }}
        />
        <Text {...styles.separator} />
        <SettingsDrawerButton
          name="Privacy Policy"
          icon={FileText}
          action={() => {
            handlePrivacyPress();
          }}
        />
        <Text {...styles.separator} />
        <SettingsDrawerButton
          name="Terms"
          icon={Briefcase}
          action={() => {
            handleTermsPress();
          }}
        />
        <Text {...styles.separator} />
        <SettingsDrawerButton
          name="Logout"
          icon={LogOut}
          action={() => {
            console.log("logout");
            signOut();
          }}
        />
      </YStack>
    );
  }
);

const ProfileSection = memo(({ user }: { user: User | null }) => {
  const styles = useStyles();
  const [isEditing, setIsEditing] = useState(false);
  const facilityData = [
    { name: "Hospital A", id: "1" },
    { name: "Clinic B", id: "2" },
    { name: "Medical Center C", id: "3" },
  ];

  const initials = user?.firstName
    ? user?.firstName.charAt(0).toUpperCase() +
      user?.lastName.charAt(0).toUpperCase()
    : "User";

  return (
    <YStack {...styles.profileContainer}>
      <Card {...styles.profileCard}>
        <YStack {...styles.profileContent}>
          <Avatar circular size="$6">
            <Avatar.Fallback
              style={{
                backgroundColor: "#1570EF",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text style={{ color: "white" }}>{initials}</Text>
            </Avatar.Fallback>
          </Avatar>
          <Text {...styles.profileText}>
            {user?.firstName + " " + user?.lastName}
          </Text>
          {user?.role !== "nurse" && (
            <Text {...styles.emailText}>{user?.email}</Text>
          )}
          {!isEditing && (
            <Button {...styles.editButton} onPress={() => setIsEditing(true)}>
              Edit Profile
            </Button>
          )}
        </YStack>
        {isEditing && (
          <YStack marginInline={20} marginBlock={5}>
            <EditProfile onClose={() => setIsEditing(false)} />
          </YStack>
        )}
        {/* <Text {...styles.separator} />
        <YStack {...styles.facilityContainer}>
          <Text {...styles.facilityTitle}>FACILITY</Text>
          <FacilityDrawer
            data={facilityData}
            placeholder="Select a Facility"
            onSelect={() => {}}
          />
        </YStack> */}
      </Card>
    </YStack>
  );
});

export function SheetContents({
  setOpen,
  user,
}: {
  setOpen: (open: boolean) => void;
  user: User | null;
}) {
  return (
    <YStack>
      <ProfileSection user={user} />
      <SettingsSection setOpen={setOpen} user={user} />
    </YStack>
  );
}

export default SheetDemo;

const useStyles = () => {
  return {
    sheetOverlay: {
      backgroundColor: "transparent" as "transparent",
    },
    sheetFrame: {
      padding: "$5",
      backgroundColor: "$screenBackgroundcolor",
    },
    separator: {
      height: 1,
      backgroundColor: "$primaryBorderColor",
    },
    sectionContainer: {
      flex: 1,
      width: "100%" as "100%",
      marginBlock: 10,
      backgroundColor: "$screenBackgroundcolor",
    },
    profileContainer: {
      borderRadius: "$5" as "$5",
      backgroundColor: "$screenBackgroundcolor",
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
    },
    profileCard: {
      paddingBlock: 20,
      backgroundColor: "$screenBackgroundcolor" as any,
    },
    profileContent: {
      justifyContent: "center",
      alignItems: "center",
    },
    profileText: {
      fontWeight: 500 as any,
      fontSize: 14,
      marginBlock: 10,
      marginInline: 10,
    },
    emailText: {
      fontWeight: 400 as any,
      fontSize: 14,
      marginInline: 10,
    },
    editButton: {
      backgroundColor: "$screenBackgroundcolor",
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      paddingInline: 25,
      marginBlock: 10,
      fontWeight: 600 as any,
      fontSize: 14,
    },
    facilityContainer: {
      marginBlockStart: 20,
      marginInline: 20,
    },
    facilityTitle: {
      fontWeight: 600 as any,
      fontSize: 14,
      marginBlockEnd: 10,
    },
  };
};
