import { useRouter } from "expo-router";
import * as SecureStore from "expo-secure-store";
import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";
import { AppState } from "react-native";
import axiosConfig, { eventEmitter } from "~/services/axiosConfig";
import * as WebBrowser from "expo-web-browser";
import * as Linking from "expo-linking";
import Constants from "expo-constants";

const CLIENT_ID = "NPkZ5BbGTmnxgFH0d5UNjvEOcvqkV9Hb";
const REDIRECT_URI = "https://www.api.vitalcare.org/api/v1/pcc/auth/callback";
// Constants.expoConfig?.extra?.REDIRECT_URI ||
// "https://www.staging.vitalcare.org/api/v1/pcc/auth/callback";
// "https://bbb1-174-74-175-40.ngrok-free.app/api/v1/pcc/auth/callback";
const PCC_AUTH_URL = "https://connect.pointclickcare.com/auth/login";
// Constants.expoConfig?.extra?.PCC_AUTH_URL ||
// "https://connect.pointclickcare.com/auth/login";

const buildPCCAuthUrl = (): string => {
  const params = new URLSearchParams({
    client_id: CLIENT_ID,
    response_type: "code",
    redirect_uri: REDIRECT_URI,
    scope: "openid",
    // state: "someRandomCSRFToken", // TODO: Ideally, generate this per session
  });
  return `${PCC_AUTH_URL}?${params.toString()}`;
};

interface User {
  token: string;
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: "nurse" | "provider";
  isOnline?: boolean;
  phoneNumber?: string;
  pin?: string;
}

interface AuthContextType {
  user: User | null;
  localUser: User | null;
  loading: boolean;
  signIn: (
    email: string,
    password: string
  ) => Promise<{ success: boolean; error?: string }>;
  signInWithPCC: () => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  getUser: () => Promise<User | null>;
  updateUser: (firstName: string, lastName: string) => Promise<void>;
  updatePhone: (phoneNumber: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps): JSX.Element => {
  const [user, setUser] = useState<User | null>(null);
  const [localUser, setLocalUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const router = useRouter();

  const revokeToken = async () => {
    console.log("Revoking token...");
    await axiosConfig.post("/auth/logout");
    try {
      await SecureStore.deleteItemAsync("authToken");
      setUser(null);
      setLocalUser(null);
    } catch (error) {
      console.error("Error revoking token:", error);
    }
  };

  useEffect(() => {
    const handleLogout = async () => {
      await revokeToken();
      router.replace("/");
    };

    eventEmitter.on("logout", handleLogout);

    return () => {
      eventEmitter.off("logout", handleLogout);
    };
  }, [router]);

  useEffect(() => {
    const loadToken = async () => {
      try {
        const token = await SecureStore.getItemAsync("authToken");
        if (token) {
          await getUser();
        }
      } catch (error) {
        console.error("Error reading auth token:", error);

        // Check if the error is related to device being locked
        if (
          error instanceof Error &&
          (error.message.includes("User interaction is not allowed") ||
            error.message.includes(
              "Calling the 'getValueWithKeyAsync' function has failed"
            ))
        ) {
          console.log(
            "Device appears to be locked during initial load, will retry when unlocked"
          );

          // Set up a listener for app state changes to retry when the app becomes active
          const subscription = AppState.addEventListener(
            "change",
            (nextAppState: string) => {
              if (nextAppState === "active") {
                console.log("App became active, retrying token load");
                loadToken();
                // Remove the listener after it's triggered
                subscription.remove();
              }
            }
          );
        }
      } finally {
        setLoading(false);
      }
    };

    loadToken();
  }, []);

  const signInWithPCC = async (): Promise<{
    success: boolean;
    error?: string;
  }> => {
    try {
      // Build the PCC auth URL and log it.
      const pccAuthUrl = buildPCCAuthUrl();

      // Open the PCC auth session with full-screen presentation.
      const result = await WebBrowser.openAuthSessionAsync(
        pccAuthUrl,
        REDIRECT_URI,
        {
          presentationStyle: WebBrowser.WebBrowserPresentationStyle.FULL_SCREEN,
        }
      );

      if (result.type !== "success") {
        console.error("PCC login was canceled or failed:", result);
        return { success: false, error: "PCC login was canceled or failed." };
      }

      // Parse the returned URL.
      const parsed = Linking.parse(result.url);
      const data = parsed.queryParams as any;

      const token = data.token;
      if (!token) {
        console.error("No access token received from PCC.", data);
        return { success: false, error: "No access token received from PCC." };
      }

      // Construct user object and log it.
      const user = {
        id: data.id,
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        role: data.role,
      };

      // Store the token in SecureStore with AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY option
      await SecureStore.setItemAsync("authToken", token, {
        keychainAccessible: SecureStore.AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY,
      });

      // Update user state.
      setUser({ token, ...user });
      setLocalUser({ token, ...user });

      return { success: true };
    } catch (error: any) {
      console.error("Error in signInWithPCC:", error);
      return { success: false, error: error.message };
    }
  };

  const signIn = async (
    email: string,
    password: string
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await axiosConfig.post(`/auth/login`, {
        email,
        password,
      });
      const token = response.data.token;
      const userData = response.data.user;
      await SecureStore.setItemAsync("authToken", token, {
        keychainAccessible: SecureStore.AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY,
      });
      setUser({ token, ...userData });
      setLocalUser({ token, ...userData });
      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        error:
          "Failed to sign in. Please check your credentials and try again.",
      };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async (): Promise<void> => {
    setLoading(true);
    try {
      await revokeToken();
    } catch (error) {
      console.error("Sign out failed:", error);
    } finally {
      setLoading(false);
    }
  };

  const getUser = async (): Promise<User | null> => {
    let token;
    try {
      if (user?.token) {
        token = user.token;
      } else {
        token = await SecureStore.getItemAsync("authToken");
      }

      if (!token) {
        console.error("No token found");
        return null;
      }

      try {
        const response = await axiosConfig.get("/user", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        const fetchedUser = response.data;
        const updatedUser = { token, ...fetchedUser };
        setUser(updatedUser);
        setLocalUser(updatedUser);
        return updatedUser;
      } catch (error) {
        console.error("Error fetching user data:", error);
        return null;
      }
    } catch (error) {
      // Handle device lock errors
      console.error("Error reading auth token:", error);

      // Check if the error is related to device being locked
      if (
        error instanceof Error &&
        (error.message.includes("User interaction is not allowed") ||
          error.message.includes(
            "Calling the 'getValueWithKeyAsync' function has failed"
          ))
      ) {
        console.log(
          "Device appears to be locked, cannot access secure storage"
        );

        // If we have a user in memory, use that
        if (user) {
          console.log("Using in-memory user data");
          return user;
        }
      }

      return null;
    }
  };

  const updateUser = async (
    firstName: string,
    lastName: string
  ): Promise<void> => {
    let token;
    if (user?.token) {
      token = user.token;
    } else {
      token = await SecureStore.getItemAsync("authToken");
    }
    if (!token) {
      console.error("No token found");
      return;
    }

    try {
      const response = await axiosConfig.put(
        "/user/name",
        { firstName, lastName }, // Request body
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200 && localUser) {
        // Update localUser only if the request succeeds with status 200
        setLocalUser({
          ...localUser,
          firstName,
          lastName,
        });
      }
    } catch (error) {
      console.error("Error updating user name:", error);
    }
  };

  const updatePhone = async (phoneNumber: string): Promise<void> => {
    let token;
    if (user?.token) {
      token = user.token;
    } else {
      token = await SecureStore.getItemAsync("authToken");
    }
    if (!token) {
      console.error("No token found");
      return;
    }

    try {
      const response = await axiosConfig.put(
        "/user/phone",
        { phoneNumber },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      if (response.status === 200 && localUser) {
        // Update localUser only if the request succeeds with status 200
        setLocalUser({
          ...localUser,
          phoneNumber,
        });
      }
    } catch (error) {
      console.error("Error updating user PhoneNumber:", error);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        localUser,
        loading,
        signIn,
        signOut,
        getUser,
        updateUser,
        updatePhone,
        signInWithPCC,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
