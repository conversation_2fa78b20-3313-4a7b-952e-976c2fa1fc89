import React, { createContext, useContext, useState } from "react";

interface ConsultationRequestData {
  consultationRequestId: string;
  facilityId: string;
  patientName: string;
  patientDOB: string;
  patientGender: string;
  location: string;
  caller: string;
  chief_complaint: string;
}

interface TimedConsultationRequestData extends ConsultationRequestData {
  timeLeft: number;
}

interface ConsultationRequestContextType {
  requestQueue: TimedConsultationRequestData[];
  setRequestQueue: React.Dispatch<
    React.SetStateAction<TimedConsultationRequestData[]>
  >;
}

const ConsultationRequestContext = createContext<
  ConsultationRequestContextType | undefined
>(undefined);

export const ConsultationRequestProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [requestQueue, setRequestQueue] = useState<
    TimedConsultationRequestData[]
  >([]);

  return (
    <ConsultationRequestContext.Provider
      value={{ requestQueue, setRequestQueue }}
    >
      {children}
    </ConsultationRequestContext.Provider>
  );
};

export const useConsultationRequest = () => {
  const context = useContext(ConsultationRequestContext);
  if (!context) {
    throw new Error(
      "useConsultationRequest must be used within a ConsultationRequestProvider"
    );
  }
  return context;
};
