import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import io, { Socket } from "socket.io-client";
import Constants from "expo-constants";
import { useAuth } from "~/context/AuthContext";
import { useAlertsContext } from "./AlertsContext";

const SOCKET_SERVER_URL = Constants.expoConfig?.extra?.apiUrl;

export interface ChatMessage {
  id: string;
  consultation_id: string;
  sender_id: string;
  message: string;
  sent_at: string;
  created_at: string;
  updated_at: string;
}

export interface ChatUpdateData {
  chatMessage: ChatMessage;
  consultationId: string;
}

export interface SocketContextType {
  socket: Socket | null;
  onChatUpdate: (callback: (data: ChatUpdateData) => void) => void;
  offChatUpdate: (callback: (data: ChatUpdateData) => void) => void;
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  onChatUpdate: () => {},
  offChatUpdate: () => {},
});

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error("useSocket must be used within a SocketProvider");
  }
  return context;
};

export const SocketProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { user } = useAuth();
  const [socket, setSocket] = useState<Socket | null>(null);
  const chatUpdateCallbacks = React.useRef<((data: ChatUpdateData) => void)[]>(
    []
  );
  const { refreshAlerts } = useAlertsContext();

  const onChatUpdate = useCallback(
    (callback: (data: ChatUpdateData) => void) => {
      chatUpdateCallbacks.current.push(callback);
    },
    []
  );

  const offChatUpdate = useCallback(
    (callback: (data: ChatUpdateData) => void) => {
      chatUpdateCallbacks.current = chatUpdateCallbacks.current.filter(
        (cb) => cb !== callback
      );
    },
    []
  );

  useEffect(() => {
    if (!user) return;

    const newSocket = io(SOCKET_SERVER_URL, {
      transports: ["websocket"],
    });

    newSocket.on("connect", () => {
      newSocket.emit("joinRoom", `user:${user.id}`);
    });

    const handleChatUpdate = (data: ChatUpdateData) => {
      chatUpdateCallbacks.current.forEach((callback) => callback(data));
      // Refresh alerts when a new message is received
      refreshAlerts();
    };

    newSocket.on("consultationChatUpdate", handleChatUpdate);

    setSocket(newSocket);

    return () => {
      newSocket.off("consultationChatUpdate", handleChatUpdate);
      newSocket.disconnect();
      setSocket(null);
    };
  }, [user, refreshAlerts]);

  return (
    <SocketContext.Provider value={{ socket, onChatUpdate, offChatUpdate }}>
      {children}
    </SocketContext.Provider>
  );
};
