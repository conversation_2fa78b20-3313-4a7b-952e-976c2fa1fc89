import { useState, useEffect, useCallback } from "react";
import axiosConfig from "../services/axiosConfig";
import { ConsulataionTypeV2, ConsultationV2 } from "src/types/consultationV2";

export interface UseConsultationInCallResponse {
  consultation: ConsultationV2 | null;
  loading: boolean;
  error: Error | null;
  refresh: () => Promise<void>;
}

export const useConsultationV2 = (
  consultationId?: string
): UseConsultationInCallResponse => {
  const [consultation, setConsultation] = useState<ConsultationV2 | null>(null);
  const [loading, setLoading] = useState<boolean>(!!consultationId);
  const [error, setError] = useState<Error | null>(null);

  const fetchConsultation = useCallback(async () => {
    if (!consultationId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await axiosConfig.get<ConsulataionTypeV2>(
        `/consultation/${consultationId}`
      );
      setConsultation(response.data?.consultation);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [consultationId]);

  useEffect(() => {
    fetchConsultation();
  }, [fetchConsultation]);

  return { consultation, loading, error, refresh: fetchConsultation };
};
