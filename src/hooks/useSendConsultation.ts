import { useState } from "react";
import axios from "../services/axiosConfig";

const useSendTranscript = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [responseData, setResponseData] = useState<any>(null);

  const sendTranscript = async (consultationId: string, transcript: any) => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.post("/consultation/transcript", {
        consultationId,
        transcript: JSON.stringify(transcript),
      });

      setResponseData(response.data);
      console.log("Transcript sent successfully:", response.data);
      return response.data;
    } catch (err: any) {
      const errorMsg =
        err.response?.data?.message ||
        err.message ||
        "Failed to send transcript";
      setError(errorMsg);
      console.error("Error sending transcript:", errorMsg);
    } finally {
      setLoading(false);
    }
  };

  return {
    sendTranscript,
    loading,
    error,
    responseData,
  };
};

export default useSendTranscript;
