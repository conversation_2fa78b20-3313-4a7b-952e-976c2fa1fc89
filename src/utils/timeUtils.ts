export function getTimeDifference(sentAt: string): string {
  const sentTime: Date = new Date(sentAt);
  const now: Date = new Date();
  const diffMs: number = Math.abs(now.getTime() - sentTime.getTime());

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  if (diffMs < minute) {
    return "just now";
  } else if (diffMs < hour) {
    const mins = Math.floor(diffMs / minute);
    return `${mins}min`;
  } else if (diffMs < day) {
    const hrs = Math.floor(diffMs / hour);
    const mins = Math.floor((diffMs % hour) / minute);
    return mins > 0 ? `${hrs}hr ${mins}min` : `${hrs}hr`;
  } else if (diffMs < week) {
    const days = Math.floor(diffMs / day);
    return `${days} day${days !== 1 ? "s" : ""}`;
  } else {
    return sentTime.toLocaleDateString("en-US", {
      month: "short",
      day: "2-digit",
    });
  }
}
