export const displayConsultationStatus = (status: string) => {
  switch (status) {
    case "created":
      return "Pending";
    case "started":
      return "Pending";
    case "completed":
      return "Pending";
    case "submitted":
      return "Ready for Billing";
    default:
      return status;
  }
};

export const displayDate = (date: string) => {
  const d = new Date(date);
  return d.toLocaleDateString("en-US", {
    year: "numeric",
    month: "numeric",
    day: "numeric",
  });
};

export const displayTime = (time: string) => {
  const d = new Date(time);
  return d.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  });
};
